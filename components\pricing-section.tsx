"use client"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Check, TrendingUp, Target, Clock, Shield, DollarSign, Zap } from "lucide-react"
import { useState } from "react"

export default function PricingSection() {
  const [selectedCurrency, setSelectedCurrency] = useState("USD")
  const [selectedBalance, setSelectedBalance] = useState(25000)

  const currencies = [
    { code: "USD", flag: "/images/flags/usd.png", name: "US Dollar" },
    { code: "EUR", flag: "/images/flags/eur.png", name: "Euro" },
    { code: "GBP", flag: "/images/flags/gbp.png", name: "British Pound" },
    { code: "CAD", flag: "/images/flags/cad.png", name: "Canadian Dollar" },
    { code: "AUD", flag: "/images/flags/aud.png", name: "Australian Dollar" },
    { code: "JPY", flag: "/images/flags/jpy.png", name: "Japanese Yen" },
    { code: "CHF", flag: "/images/flags/chf.png", name: "Swiss Franc" },
  ]

  const balanceOptions = [25000, 50000, 75000, 100000, 200000]

  const tradingObjectives = [
    {
      parameter: "Trading Period",
      icon: Clock,
      step1: "Unlimited",
      step2: "Unlimited",
      step3: "Unlimited",
      description: "No time limits on any phase",
    },
    {
      parameter: "Maximum Trading Days",
      icon: Target,
      step1: "4 Days",
      step2: "4 Days",
      step3: "5",
      description: "Minimum trading days required",
    },
    {
      parameter: "Maximum Daily Loss",
      icon: Shield,
      step1: "$500",
      step2: "$500",
      step3: "$500",
      description: "Daily drawdown limit",
    },
    {
      parameter: "Maximum Loss",
      icon: TrendingUp,
      step1: "$1,000",
      step2: "$1,000",
      step3: "$1,000",
      description: "Total drawdown limit",
    },
    {
      parameter: "Profit Target",
      icon: DollarSign,
      step1: "$2,000",
      step2: "$500",
      step3: "5",
      description: "Required profit to advance",
    },
    {
      parameter: "Refundable Fee",
      icon: Zap,
      step1: "$99",
      step2: "Free",
      step3: "Refund",
      description: "One-time evaluation fee",
    },
  ]

  const getPriceForBalance = (balance: number) => {
    const priceMap: { [key: number]: number } = {
      25000: 99,
      50000: 199,
      75000: 299,
      100000: 399,
      200000: 799,
    }
    return priceMap[balance] || 99
  }

  return (
    <section className="py-24 md:py-32 relative" aria-labelledby="pricing-heading">
      <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
        {/* Section Header */}
        <div className="mb-16 md:mb-20 text-center">
          <Badge
            variant="outline"
            className="mb-8 md:mb-12 text-sm font-light border-gray-300 dark:border-white/20 text-gray-600 dark:text-white/80 px-4 py-2 items-center"
          >
            <TrendingUp className="w-4 h-4 mr-2" />
            Trading Objectives
          </Badge>
          <h2
            id="pricing-heading"
            className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 md:mb-8 leading-tight text-gray-900 dark:text-white"
          >
            TRADING OBJECTIVES
          </h2>
          <p className="text-lg md:text-xl text-gray-700 dark:text-white/70 leading-relaxed max-w-4xl mx-auto">
            Fund trading objectives, rules and requirements. Clients can manage their effectively. By adhering to these
            rules, clients can ensure their safety and build healthy and sustainable trading habits.
          </p>
          <p className="text-base text-gray-600 dark:text-white/60 mt-4 max-w-4xl mx-auto">
            Start with our Two Challenge and Verification. Clients gain access to our Tier Account. These objectives are
            designed to place emphasis on the Profit Target.
          </p>
        </div>

        {/* Currency Selection */}
        <div className="mb-8">
          <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">CURRENCY</h3>
          <div className="flex flex-wrap gap-3">
            {currencies.map((currency) => (
              <button
                key={currency.code}
                onClick={() => setSelectedCurrency(currency.code)}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all duration-300 hover:scale-105 ${
                  selectedCurrency === currency.code
                    ? "bg-green-600 dark:bg-green-500 text-white border-green-600 dark:border-green-500"
                    : "bg-white dark:bg-white/5 text-gray-700 dark:text-white/70 border-gray-200 dark:border-white/20 hover:border-green-300 dark:hover:border-green-400/30"
                }`}
              >
                <img
                  src={currency.flag || "/placeholder.svg"}
                  alt={`${currency.name} flag`}
                  className="w-6 h-4 object-cover rounded-sm"
                  crossOrigin="anonymous"
                />
                <span className="font-medium">{currency.code}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Balance Selection */}
        <div className="mb-12">
          <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">BALANCE</h3>
          <div className="flex flex-wrap gap-3 mb-6">
            {balanceOptions.map((balance) => (
              <button
                key={balance}
                onClick={() => setSelectedBalance(balance)}
                className={`px-6 py-3 rounded-lg border font-bold transition-all duration-300 hover:scale-105 ${
                  selectedBalance === balance
                    ? "bg-green-600 dark:bg-green-500 text-white border-green-600 dark:border-green-500"
                    : "bg-white dark:bg-white/5 text-gray-700 dark:text-white/70 border-gray-200 dark:border-white/20 hover:border-green-300 dark:hover:border-green-400/30"
                }`}
              >
                ${balance.toLocaleString()}
              </button>
            ))}
          </div>
          <div className="flex justify-end">
            <Button
              variant="outline"
              className="border-gray-300 dark:border-white/20 text-gray-700 dark:text-white/70 hover:bg-gray-50 dark:hover:bg-white/5 bg-transparent"
            >
              QUICK COMPARISON
            </Button>
          </div>
        </div>

        {/* Trading Objectives Table */}
        <div className="bg-white/95 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-2xl overflow-hidden shadow-xl">
          {/* Table Header */}
          <div className="bg-gradient-to-r from-gray-50/80 to-gray-100/80 dark:from-white/5 dark:to-white/10 px-6 py-4 border-b border-gray-200 dark:border-white/10">
            <div className="grid grid-cols-12 gap-4 items-center">
              <div className="col-span-4"></div>
              <div className="col-span-2 text-center">
                <div className="text-sm font-bold text-gray-900 dark:text-white mb-1">STEP 1</div>
                <div className="text-xs text-green-600 dark:text-green-400 font-medium">TWO CHALLENGE</div>
              </div>
              <div className="col-span-2 text-center">
                <div className="text-sm font-bold text-gray-900 dark:text-white mb-1">STEP 2</div>
                <div className="text-xs text-blue-600 dark:text-blue-400 font-medium">VERIFICATION</div>
              </div>
              <div className="col-span-2 text-center">
                <div className="text-sm font-bold text-gray-900 dark:text-white mb-1">STEP 3</div>
                <div className="text-xs text-purple-600 dark:text-purple-400 font-medium">LIVE TRADING</div>
              </div>
              <div className="col-span-2"></div>
            </div>
          </div>

          {/* Table Body */}
          <div className="divide-y divide-gray-200 dark:divide-white/10">
            {tradingObjectives.map((objective, index) => {
              const IconComponent = objective.icon
              return (
                <div
                  key={index}
                  className="px-6 py-4 hover:bg-gray-50/50 dark:hover:bg-white/5 transition-colors duration-200"
                >
                  <div className="grid grid-cols-12 gap-4 items-center">
                    {/* Parameter Name */}
                    <div className="col-span-4 flex items-center gap-3">
                      <div className="w-2 h-2 rounded-full bg-green-500"></div>
                      <div className="flex items-center gap-2">
                        <IconComponent className="w-4 h-4 text-gray-500 dark:text-white/50" />
                        <span className="text-sm font-medium text-gray-900 dark:text-white">{objective.parameter}</span>
                      </div>
                    </div>

                    {/* Step 1 */}
                    <div className="col-span-2 text-center">
                      <span className="text-sm font-bold text-gray-900 dark:text-white">{objective.step1}</span>
                    </div>

                    {/* Step 2 */}
                    <div className="col-span-2 text-center">
                      <span className="text-sm font-bold text-gray-900 dark:text-white">{objective.step2}</span>
                    </div>

                    {/* Step 3 */}
                    <div className="col-span-2 text-center">
                      <span className="text-sm font-bold text-gray-900 dark:text-white">{objective.step3}</span>
                    </div>

                    {/* Status */}
                    <div className="col-span-2 text-center">
                      <div className="w-6 h-6 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center mx-auto">
                        <Check className="w-4 h-4 text-green-600 dark:text-green-400" />
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>

          {/* Footer Note */}
          <div className="px-6 py-4 bg-gray-50/50 dark:bg-white/5 border-t border-gray-200 dark:border-white/10">
            <p className="text-xs text-gray-600 dark:text-white/60 leading-relaxed">
              <span className="text-green-600 dark:text-green-400 font-medium">Account size:</span> You can trade up to
              $25,000 | <span className="text-blue-600 dark:text-blue-400 font-medium">Challenge fee:</span> This amount
              is limited to all refunds after successfully completing the first Challenge |{" "}
              <span className="text-purple-600 dark:text-purple-400 font-medium">Free trial account:</span>
              Available
            </p>
          </div>
        </div>

        {/* Pricing Display */}
        <div className="mt-8 text-center">
          <div className="inline-flex items-center gap-4 bg-gradient-to-r from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20 backdrop-blur-sm border border-green-200 dark:border-green-400/20 rounded-2xl px-8 py-4">
            <div className="text-center">
              <div className="text-sm text-gray-600 dark:text-white/60 mb-1">Challenge Fee</div>
              <div className="text-3xl font-bold text-green-600 dark:text-green-400">
                ${getPriceForBalance(selectedBalance)}
              </div>
            </div>
            <div className="w-px h-12 bg-gray-300 dark:bg-white/20"></div>
            <div className="text-center">
              <div className="text-sm text-gray-600 dark:text-white/60 mb-1">Account Size</div>
              <div className="text-3xl font-bold text-gray-900 dark:text-white">
                ${selectedBalance.toLocaleString()}
              </div>
            </div>
          </div>
        </div>

        {/* CTA Button */}
        <div className="mt-12 text-center">
          <Button className="rounded-full bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-500 dark:to-emerald-500 text-white hover:from-green-700 hover:to-emerald-700 dark:hover:from-green-600 dark:hover:to-emerald-600 px-12 py-4 text-lg font-bold hover:scale-105 transition-all duration-300 hover:shadow-xl">
            START YOUR CHALLENGE
          </Button>
        </div>

        {/* Additional Benefits */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-6 bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-2xl hover:scale-105 transition-all duration-300">
            <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-green-500/20 to-emerald-500/20 dark:from-green-500/30 dark:to-emerald-500/30 flex items-center justify-center mx-auto mb-4">
              <Target className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">No Time Limits</h4>
            <p className="text-sm text-gray-600 dark:text-white/60">Take as long as you need to complete each phase</p>
          </div>
          <div className="text-center p-6 bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-2xl hover:scale-105 transition-all duration-300">
            <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-500/20 to-cyan-500/20 dark:from-blue-500/30 dark:to-cyan-500/30 flex items-center justify-center mx-auto mb-4">
              <Shield className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">Risk Management</h4>
            <p className="text-sm text-gray-600 dark:text-white/60">Clear rules to protect your trading capital</p>
          </div>
          <div className="text-center p-6 bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-2xl hover:scale-105 transition-all duration-300">
            <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 dark:from-purple-500/30 dark:to-pink-500/30 flex items-center justify-center mx-auto mb-4">
              <Zap className="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
            <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">Instant Funding</h4>
            <p className="text-sm text-gray-600 dark:text-white/60">Get funded within 24-48 hours after passing</p>
          </div>
        </div>
      </div>
    </section>
  )
}
