"use client"

import { Badge } from "@/components/ui/badge"
import { Star, Quote, TrendingUp, DollarSign, Award, User } from "lucide-react"
import { useState } from "react"

export default function TestimonialsSection() {
  const [activeTestimonial, setActiveTestimonial] = useState(0)

  const testimonials = [
    {
      name: "<PERSON>",
      role: "Professional Forex Trader",
      location: "New York, USA",
      avatar: "/placeholder-user.jpg",
      rating: 5,
      profit: "$47,500",
      timeframe: "6 months",
      quote: "Apex Capital transformed my trading career. The evaluation process was fair, and once funded, I had access to incredible capital and support. I've consistently grown my account and the profit splits are exactly as promised.",
      highlight: "Scaled from $25K to $100K account",
    },
    {
      name: "<PERSON>",
      role: "Algorithmic Trading Specialist",
      location: "London, UK",
      avatar: "/placeholder-user.jpg",
      rating: 5,
      profit: "$89,200",
      timeframe: "8 months",
      quote: "As someone who relies heavily on automated trading systems, I was impressed by Apex Capital's EA-friendly policies. Their execution speed is exceptional, and the risk management tools are top-notch.",
      highlight: "Achieved 90% profit split tier",
    },
    {
      name: "<PERSON>",
      role: "Former Investment Banker",
      location: "Toronto, Canada",
      avatar: "/placeholder-user.jpg",
      rating: 5,
      profit: "$156,800",
      timeframe: "12 months",
      quote: "After 15 years in traditional finance, I was skeptical about prop trading firms. Apex Capital exceeded all expectations with their professionalism, transparency, and genuine commitment to trader success.",
      highlight: "Manages $200K funded account",
    },
    {
      name: "Elena Kowalski",
      role: "Day Trading Expert",
      location: "Berlin, Germany",
      avatar: "/placeholder-user.jpg",
      rating: 5,
      profit: "$73,400",
      timeframe: "9 months",
      quote: "The educational resources and mentorship program at Apex Capital are unmatched. They don't just give you capital; they help you become a better trader. The community aspect is incredible.",
      highlight: "Completed advanced trader program",
    },
    {
      name: "James Park",
      role: "Swing Trading Strategist",
      location: "Sydney, Australia",
      avatar: "/placeholder-user.jpg",
      rating: 5,
      profit: "$92,100",
      timeframe: "10 months",
      quote: "What sets Apex Capital apart is their understanding that different traders have different styles. They accommodate my swing trading approach perfectly, and the weekend holding policy is a game-changer.",
      highlight: "Consistently profitable for 10 months",
    },
  ]

  const stats = [
    {
      value: "98.7%",
      label: "Trader Satisfaction Rate",
      icon: Star,
      color: "text-yellow-600 dark:text-yellow-400",
    },
    {
      value: "4.9/5",
      label: "Average Rating",
      icon: Award,
      color: "text-green-600 dark:text-green-400",
    },
    {
      value: "$50M+",
      label: "Total Payouts",
      icon: DollarSign,
      color: "text-blue-600 dark:text-blue-400",
    },
    {
      value: "15K+",
      label: "Funded Traders",
      icon: TrendingUp,
      color: "text-purple-600 dark:text-purple-400",
    },
  ]

  return (
    <section className="py-24 md:py-32 relative" aria-labelledby="testimonials-heading">
      <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
        {/* Section Header */}
        <div className="mb-20 md:mb-28 max-w-4xl mx-auto text-center">
          <Badge
            variant="outline"
            className="mb-8 md:mb-12 text-sm font-light border-gray-300 dark:border-white/20 text-gray-600 dark:text-white/80 px-4 py-2 items-center"
          >
            <Quote className="w-4 h-4 mr-2" />
            Trader Success Stories
          </Badge>
          <h2 id="testimonials-heading" className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 md:mb-12 leading-tight">
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent">
              Trusted
            </span>{" "}
            by professionals
          </h2>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 leading-relaxed">
            Join thousands of successful traders who have built profitable careers with our funding and support.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-20">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon
            return (
              <div
                key={index}
                className="text-center p-6 bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-2xl hover:scale-105 transition-all duration-300"
              >
                <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-gray-100 to-gray-200 dark:from-white/10 dark:to-white/20 flex items-center justify-center mx-auto mb-4">
                  <IconComponent className={`w-6 h-6 ${stat.color}`} />
                </div>
                <div className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-2">{stat.value}</div>
                <div className="text-sm text-gray-600 dark:text-white/60">{stat.label}</div>
              </div>
            )
          })}
        </div>

        {/* Main Testimonial */}
        <div className="relative">
          <div className="absolute -top-10 -left-10 w-40 h-40 rounded-full bg-gradient-to-r from-blue-500/10 to-purple-500/10 dark:from-blue-500/20 dark:to-purple-500/20 blur-3xl" />
          <div className="absolute -bottom-10 -right-10 w-40 h-40 rounded-full bg-gradient-to-r from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20 blur-3xl" />

          <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 md:p-12 shadow-xl relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 dark:from-blue-500/10 dark:to-purple-500/10" />
            
            <div className="relative z-10">
              {/* Quote Icon */}
              <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-blue-500/20 to-purple-500/20 dark:from-blue-500/30 dark:to-purple-500/30 flex items-center justify-center mb-8">
                <Quote className="w-8 h-8 text-blue-600 dark:text-blue-400" />
              </div>

              {/* Testimonial Content */}
              <div className="mb-8">
                <p className="text-xl md:text-2xl text-gray-900 dark:text-white leading-relaxed mb-6">
                  "{testimonials[activeTestimonial].quote}"
                </p>
                <div className="inline-flex items-center gap-2 bg-gradient-to-r from-green-500/20 to-emerald-500/20 dark:from-green-500/30 dark:to-emerald-500/30 rounded-xl px-4 py-2">
                  <Award className="w-4 h-4 text-green-600 dark:text-green-400" />
                  <span className="text-sm font-medium text-green-600 dark:text-green-400">
                    {testimonials[activeTestimonial].highlight}
                  </span>
                </div>
              </div>

              {/* Author Info */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-gray-200 to-gray-300 dark:from-white/20 dark:to-white/30 flex items-center justify-center">
                    <User className="w-8 h-8 text-gray-600 dark:text-white/60" />
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-gray-900 dark:text-white">
                      {testimonials[activeTestimonial].name}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-white/60">
                      {testimonials[activeTestimonial].role}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-white/50">
                      {testimonials[activeTestimonial].location}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">
                    {testimonials[activeTestimonial].profit}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-white/60">
                    in {testimonials[activeTestimonial].timeframe}
                  </div>
                  <div className="flex items-center gap-1 mt-2">
                    {[...Array(testimonials[activeTestimonial].rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Testimonial Navigation */}
        <div className="flex justify-center mt-12 gap-3">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setActiveTestimonial(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === activeTestimonial
                  ? "bg-blue-600 dark:bg-blue-400 scale-125"
                  : "bg-gray-300 dark:bg-white/20 hover:bg-gray-400 dark:hover:bg-white/30"
              }`}
              aria-label={`View testimonial ${index + 1}`}
            />
          ))}
        </div>

        {/* Additional Testimonials Grid */}
        <div className="mt-20 grid grid-cols-1 md:grid-cols-2 gap-8">
          {testimonials.slice(1, 3).map((testimonial, index) => (
            <div
              key={index}
              className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-2xl p-6 hover:scale-[1.02] transition-all duration-300"
            >
              <div className="flex items-center gap-1 mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                ))}
              </div>
              <p className="text-gray-700 dark:text-white/70 mb-4 leading-relaxed">
                "{testimonial.quote.substring(0, 120)}..."
              </p>
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-bold text-gray-900 dark:text-white">{testimonial.name}</h4>
                  <p className="text-sm text-gray-600 dark:text-white/60">{testimonial.role}</p>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-green-600 dark:text-green-400">
                    {testimonial.profit}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-white/50">
                    {testimonial.timeframe}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}
