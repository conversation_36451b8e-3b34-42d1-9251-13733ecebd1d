"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { X, Zap, Clock } from "lucide-react"
import { useState, useEffect } from "react"

export default function SaleBanner() {
  const [isVisible, setIsVisible] = useState(true)
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
  })

  // Countdown timer
  useEffect(() => {
    const targetDate = new Date()
    targetDate.setDate(targetDate.getDate() + 3) // 3 days from now
    targetDate.setHours(23, 59, 59, 999)

    const timer = setInterval(() => {
      const now = new Date().getTime()
      const distance = targetDate.getTime() - now

      if (distance > 0) {
        setTimeLeft({
          days: Math.floor(distance / (1000 * 60 * 60 * 24)),
          hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((distance % (1000 * 60)) / 1000),
        })
      } else {
        setIsVisible(false)
        clearInterval(timer)
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  if (!isVisible) return null

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-red-600 via-pink-600 to-purple-600 dark:from-red-500 dark:via-pink-500 dark:to-purple-500 text-white shadow-lg">
      <div className="max-w-7xl mx-auto px-4 py-3">
        <div className="flex items-center justify-between gap-4">
          <div className="flex items-center gap-4 flex-1">
            {/* Flash Sale Badge */}
            <Badge className="bg-white/20 text-white border-white/30 text-xs px-2 py-1 animate-pulse">
              <Zap className="w-3 h-3 mr-1" />
              FLASH SALE
            </Badge>

            {/* Sale Message */}
            <div className="flex items-center gap-2 text-sm md:text-base font-medium">
              <span className="hidden sm:inline">🔥</span>
              <span>
                <strong>50% OFF</strong> All Trading Challenges - Limited Time Only!
              </span>
            </div>

            {/* Countdown Timer */}
            <div className="hidden md:flex items-center gap-2 text-sm">
              <Clock className="w-4 h-4" />
              <span>Ends in:</span>
              <div className="flex items-center gap-1">
                <div className="bg-white/20 rounded px-2 py-1 min-w-[2rem] text-center">
                  {String(timeLeft.days).padStart(2, "0")}
                </div>
                <span>:</span>
                <div className="bg-white/20 rounded px-2 py-1 min-w-[2rem] text-center">
                  {String(timeLeft.hours).padStart(2, "0")}
                </div>
                <span>:</span>
                <div className="bg-white/20 rounded px-2 py-1 min-w-[2rem] text-center">
                  {String(timeLeft.minutes).padStart(2, "0")}
                </div>
                <span>:</span>
                <div className="bg-white/20 rounded px-2 py-1 min-w-[2rem] text-center">
                  {String(timeLeft.seconds).padStart(2, "0")}
                </div>
              </div>
            </div>
          </div>

          {/* CTA and Close */}
          <div className="flex items-center gap-3">
            <Button
              size="sm"
              className="bg-white text-red-600 hover:bg-white/90 px-4 py-2 text-sm font-medium rounded-full hover:scale-105 transition-all duration-300"
            >
              Claim Offer
            </Button>
            <button
              onClick={() => setIsVisible(false)}
              className="p-1 hover:bg-white/20 rounded-full transition-colors duration-200"
              aria-label="Close banner"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Mobile Countdown */}
        <div className="md:hidden mt-2 flex items-center justify-center gap-2 text-xs">
          <Clock className="w-3 h-3" />
          <span>Ends in:</span>
          <div className="flex items-center gap-1">
            <span className="bg-white/20 rounded px-1.5 py-0.5">{String(timeLeft.days).padStart(2, "0")}d</span>
            <span className="bg-white/20 rounded px-1.5 py-0.5">{String(timeLeft.hours).padStart(2, "0")}h</span>
            <span className="bg-white/20 rounded px-1.5 py-0.5">{String(timeLeft.minutes).padStart(2, "0")}m</span>
            <span className="bg-white/20 rounded px-1.5 py-0.5">{String(timeLeft.seconds).padStart(2, "0")}s</span>
          </div>
        </div>
      </div>
    </div>
  )
}
