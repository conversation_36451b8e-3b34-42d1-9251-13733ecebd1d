"use client"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Globe, MessageCircle, Clock } from "lucide-react"
import { useState } from "react"

export default function LanguageSupport() {
  const [hoveredLanguage, setHoveredLanguage] = useState<string | null>(null)

  const languages = [
    {
      name: "English",
      code: "en",
      flag: "https://flagcdn.com/w40/us.png",
      nativeName: "English",
      supportHours: "24/7",
      popular: true,
    },
    {
      name: "Spanish",
      code: "es",
      flag: "https://flagcdn.com/w40/es.png",
      nativeName: "Español",
      supportHours: "24/7",
      popular: true,
    },
    {
      name: "French",
      code: "fr",
      flag: "https://flagcdn.com/w40/fr.png",
      nativeName: "Français",
      supportHours: "24/7",
      popular: true,
    },
    {
      name: "German",
      code: "de",
      flag: "https://flagcdn.com/w40/de.png",
      nativeName: "Deutsch",
      supportHours: "24/7",
      popular: true,
    },
    {
      name: "Italian",
      code: "it",
      flag: "https://flagcdn.com/w40/it.png",
      nativeName: "Italiano",
      supportHours: "16/7",
      popular: false,
    },
    {
      name: "Portuguese",
      code: "pt",
      flag: "https://flagcdn.com/w40/pt.png",
      nativeName: "Português",
      supportHours: "16/7",
      popular: false,
    },
    {
      name: "Russian",
      code: "ru",
      flag: "https://flagcdn.com/w40/ru.png",
      nativeName: "Русский",
      supportHours: "16/7",
      popular: false,
    },
    {
      name: "Chinese",
      code: "zh",
      flag: "https://flagcdn.com/w40/cn.png",
      nativeName: "中文",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Japanese",
      code: "ja",
      flag: "https://flagcdn.com/w40/jp.png",
      nativeName: "日本語",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Korean",
      code: "ko",
      flag: "https://flagcdn.com/w40/kr.png",
      nativeName: "한국어",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Arabic",
      code: "ar",
      flag: "https://flagcdn.com/w40/sa.png",
      nativeName: "العربية",
      supportHours: "16/7",
      popular: false,
    },
    {
      name: "Hindi",
      code: "hi",
      flag: "https://flagcdn.com/w40/in.png",
      nativeName: "हिन्दी",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Turkish",
      code: "tr",
      flag: "https://flagcdn.com/w40/tr.png",
      nativeName: "Türkçe",
      supportHours: "16/7",
      popular: false,
    },
    {
      name: "Dutch",
      code: "nl",
      flag: "https://flagcdn.com/w40/nl.png",
      nativeName: "Nederlands",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Swedish",
      code: "sv",
      flag: "https://flagcdn.com/w40/se.png",
      nativeName: "Svenska",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Norwegian",
      code: "no",
      flag: "https://flagcdn.com/w40/no.png",
      nativeName: "Norsk",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Danish",
      code: "da",
      flag: "https://flagcdn.com/w40/dk.png",
      nativeName: "Dansk",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Polish",
      code: "pl",
      flag: "https://flagcdn.com/w40/pl.png",
      nativeName: "Polski",
      supportHours: "16/7",
      popular: false,
    },
    {
      name: "Czech",
      code: "cs",
      flag: "https://flagcdn.com/w40/cz.png",
      nativeName: "Čeština",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Hungarian",
      code: "hu",
      flag: "https://flagcdn.com/w40/hu.png",
      nativeName: "Magyar",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Greek",
      code: "el",
      flag: "https://flagcdn.com/w40/gr.png",
      nativeName: "Ελληνικά",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Hebrew",
      code: "he",
      flag: "https://flagcdn.com/w40/il.png",
      nativeName: "עברית",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Thai",
      code: "th",
      flag: "https://flagcdn.com/w40/th.png",
      nativeName: "ไทย",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Vietnamese",
      code: "vi",
      flag: "https://flagcdn.com/w40/vn.png",
      nativeName: "Tiếng Việt",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Indonesian",
      code: "id",
      flag: "https://flagcdn.com/w40/id.png",
      nativeName: "Bahasa Indonesia",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Malay",
      code: "ms",
      flag: "https://flagcdn.com/w40/my.png",
      nativeName: "Bahasa Melayu",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Filipino",
      code: "fil",
      flag: "https://flagcdn.com/w40/ph.png",
      nativeName: "Filipino",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Finnish",
      code: "fi",
      flag: "https://flagcdn.com/w40/fi.png",
      nativeName: "Suomi",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Romanian",
      code: "ro",
      flag: "https://flagcdn.com/w40/ro.png",
      nativeName: "Română",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Bulgarian",
      code: "bg",
      flag: "https://flagcdn.com/w40/bg.png",
      nativeName: "Български",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Croatian",
      code: "hr",
      flag: "https://flagcdn.com/w40/hr.png",
      nativeName: "Hrvatski",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Serbian",
      code: "sr",
      flag: "https://flagcdn.com/w40/rs.png",
      nativeName: "Српски",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Ukrainian",
      code: "uk",
      flag: "https://flagcdn.com/w40/ua.png",
      nativeName: "Українська",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Estonian",
      code: "et",
      flag: "https://flagcdn.com/w40/ee.png",
      nativeName: "Eesti",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Latvian",
      code: "lv",
      flag: "https://flagcdn.com/w40/lv.png",
      nativeName: "Latviešu",
      supportHours: "12/7",
      popular: false,
    },
    {
      name: "Lithuanian",
      code: "lt",
      flag: "https://flagcdn.com/w40/lt.png",
      nativeName: "Lietuvių",
      supportHours: "12/7",
      popular: false,
    },
  ]

  const getSupportBadgeColor = (hours: string) => {
    if (hours === "24/7") return "bg-green-600 dark:bg-green-500"
    if (hours === "16/7") return "bg-blue-600 dark:bg-blue-500"
    return "bg-purple-600 dark:bg-purple-500"
  }

  // Duplicate languages array for seamless loop
  const duplicatedLanguages = [...languages, ...languages]

  return (
    <section className="py-24 md:py-32 relative overflow-hidden" aria-labelledby="language-support-heading">
      <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
        {/* Section Header */}
        <div className="mb-20 md:mb-28 max-w-4xl mx-auto text-center">
          <Badge
            variant="outline"
            className="mb-8 md:mb-12 text-sm font-light border-gray-300 dark:border-white/20 text-gray-600 dark:text-white/80 px-4 py-2 items-center"
          >
            <Globe className="w-4 h-4 mr-2" />
            Global Support
          </Badge>
          <h2
            id="language-support-heading"
            className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 md:mb-12 leading-tight"
          >
            Support in{" "}
            <span className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400 bg-clip-text text-transparent">
              35+ languages
            </span>
          </h2>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 leading-relaxed">
            Get help in your native language from our multilingual support team. We're here to assist traders from
            around the world, whenever you need us.
          </p>
        </div>

        {/* Moving Language Carousel */}
        <div className="mb-16 md:mb-20 relative">
          <div className="absolute left-0 top-0 bottom-0 w-32 bg-gradient-to-r from-gray-50 via-gray-50/80 to-transparent dark:from-slate-950 dark:via-slate-950/80 dark:to-transparent z-10" />
          <div className="absolute right-0 top-0 bottom-0 w-32 bg-gradient-to-l from-gray-50 via-gray-50/80 to-transparent dark:from-slate-950 dark:via-slate-950/80 dark:to-transparent z-10" />

          <div className="overflow-hidden">
            <div className="flex animate-scroll-left">
              {duplicatedLanguages.map((language, index) => (
                <div
                  key={`${language.code}-${index}`}
                  className="flex-shrink-0 mx-4 group cursor-pointer"
                  onMouseEnter={() => setHoveredLanguage(`${language.code}-${index}`)}
                  onMouseLeave={() => setHoveredLanguage(null)}
                >
                  <div
                    className={`bg-gradient-to-br ${
                      language.supportHours === "24/7"
                        ? "from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20 border-green-200 dark:border-green-400/20"
                        : language.supportHours === "16/7"
                          ? "from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 border-blue-200 dark:border-blue-400/20"
                          : "from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20 border-purple-200 dark:border-purple-400/20"
                    } backdrop-blur-sm border rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-500 hover:scale-110 relative overflow-hidden w-48 h-32`}
                  >
                    <div
                      className={`absolute inset-0 bg-gradient-to-br ${
                        language.supportHours === "24/7"
                          ? "from-green-500/5 to-emerald-500/5 dark:from-green-500/10 dark:to-emerald-500/10"
                          : language.supportHours === "16/7"
                            ? "from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10"
                            : "from-purple-500/5 to-pink-500/5 dark:from-purple-500/10 dark:to-pink-500/10"
                      } opacity-0 group-hover:opacity-100 transition-opacity duration-500`}
                    />

                    <div className="relative z-10 text-center h-full flex flex-col justify-between">
                      <div className="mb-2 group-hover:scale-125 transition-transform duration-300">
                        <img
                          src={language.flag || "/placeholder.svg"}
                          alt={`${language.name} flag`}
                          className="w-12 h-8 mx-auto rounded shadow-sm object-cover"
                          crossOrigin="anonymous"
                        />
                      </div>
                      <div>
                        <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-1 truncate">
                          {language.name}
                        </h4>
                        <p
                          className="text-sm text-gray-600 dark:text-white/60 mb-2 truncate"
                          title={language.nativeName}
                        >
                          {language.nativeName}
                        </p>
                        <Badge className={`${getSupportBadgeColor(language.supportHours)} text-white text-xs`}>
                          <Clock className="w-3 h-3 mr-1" />
                          {language.supportHours}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center">
          <Button className="rounded-full bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 text-white hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600 px-8 py-4 text-lg hover:scale-105 transition-all duration-300 hover:shadow-lg">
            Contact Support Now
            <MessageCircle className="ml-2 h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Custom CSS for scrolling animation */}
      <style jsx>{`
        @keyframes scroll-left {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }
        
        .animate-scroll-left {
          animation: scroll-left 60s linear infinite;
        }
        
        .animate-scroll-left:hover {
          animation-play-state: paused;
        }
      `}</style>
    </section>
  )
}
