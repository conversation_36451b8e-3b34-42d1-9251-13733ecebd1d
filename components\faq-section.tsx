"use client"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { HelpCircle, MessageCircle, ArrowRight } from "lucide-react"

export default function FAQSection() {
  const faqs = [
    {
      question: "How does the evaluation process work?",
      answer:
        "Our two-phase evaluation process is designed to identify skilled traders. Phase 1 (Challenge): Achieve 8% profit target with unlimited time while maintaining risk parameters (5% daily loss limit, 10% maximum drawdown). Phase 2 (Verification): Achieve 5% profit target with same risk rules. Upon successful completion, you receive a funded account with your chosen capital amount up to $200K.",
    },
    {
      question: "What is the profit split and how does it work?",
      answer:
        "We offer industry-leading profit splits starting at 80% and scaling to 90% after your first payout. This means you keep 80-90% of all profits generated while trading our capital. Profit splits are calculated on net profits only, with no hidden fees or complex calculations. Your earnings are transparent and paid bi-weekly.",
    },
    {
      question: "What are the trading rules and restrictions?",
      answer:
        "Our rules are designed to promote consistent, professional trading. Key rules include: maximum 5% daily drawdown, 10% maximum drawdown, minimum 5 trading days before requesting payout, no news trading restrictions, weekend holding allowed, and Expert Advisors (EAs) are permitted. We focus on risk management rather than restrictive trading styles.",
    },
    {
      question: "How quickly can I get funded?",
      answer:
        "The timeline depends on your trading performance. Most traders complete Phase 1 within 2-4 weeks and Phase 2 within 1-2 weeks. Once you pass both phases, account funding typically occurs within 24-48 hours. Our streamlined process ensures you can start trading with real capital as quickly as possible.",
    },
    {
      question: "What happens if I violate a trading rule?",
      answer:
        "Rule violations result in account termination, but we believe in second chances. You can retake the evaluation at a 20% discount. We also provide detailed analytics and feedback to help you understand what went wrong and improve your trading approach for the next attempt.",
    },
    {
      question: "Can I trade news events and hold positions overnight?",
      answer:
        "Yes! Unlike many prop firms, we allow news trading and overnight/weekend position holding. We believe skilled traders should have the freedom to capitalize on all market opportunities. However, proper risk management is still required during high-volatility periods.",
    },
    {
      question: "What trading platforms do you support?",
      answer:
        "We support both MetaTrader 4 (MT4) and MetaTrader 5 (MT5) platforms. Both platforms offer advanced charting tools, Expert Advisors, custom indicators, and mobile trading capabilities. You can choose the platform that best suits your trading style and preferences.",
    },
    {
      question: "How do payouts work and how often can I withdraw?",
      answer:
        "Payouts are processed bi-weekly (every 14 days) once you meet the minimum 5 trading days requirement. You can request withdrawals through our trader dashboard, and payments are typically processed within 1-3 business days via bank transfer, PayPal, or cryptocurrency. There's no minimum withdrawal amount.",
    },
    {
      question: "Is there ongoing support after I get funded?",
      answer:
        "Funded traders receive priority support, including dedicated account managers, advanced analytics, risk management guidance, and access to our exclusive trader community. We're invested in your long-term success and provide ongoing resources to help you grow your trading career.",
    },
    {
      question: "What makes Apex Capital different from other prop firms?",
      answer:
        "We offer higher profit splits (up to 90%), more flexible trading rules, faster funding process, superior technology infrastructure, and genuine trader support. Our focus is on building long-term partnerships with skilled traders rather than just collecting evaluation fees. We succeed when you succeed.",
    },
  ]

  return (
    <section className="py-24 md:py-32 relative" aria-labelledby="faq-heading">
      <div className="max-w-4xl mx-auto px-8 md:px-12 lg:px-16">
        {/* Section Header */}
        <div className="mb-20 md:mb-28 text-center">
          <Badge
            variant="outline"
            className="mb-8 md:mb-12 text-sm font-light border-gray-300 dark:border-white/20 text-gray-600 dark:text-white/80 px-4 py-2 items-center"
          >
            <HelpCircle className="w-4 h-4 mr-2" />
            Frequently Asked Questions
          </Badge>
          <h2 id="faq-heading" className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 md:mb-12 leading-tight">
            Got{" "}
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 dark:from-purple-400 dark:to-pink-400 bg-clip-text text-transparent">
              questions
            </span>
            ?
          </h2>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 leading-relaxed">
            Find answers to the most common questions about our funding process, trading rules, and platform features.
          </p>
        </div>

        {/* FAQ Accordion */}
        <div className="relative">
          <div className="absolute -top-10 -left-10 w-40 h-40 rounded-full bg-gradient-to-r from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20 blur-3xl" />
          <div className="absolute -bottom-10 -right-10 w-40 h-40 rounded-full bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 blur-3xl" />

          <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 md:p-12 shadow-xl relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 dark:from-purple-500/10 dark:to-pink-500/10" />

            <div className="relative z-10">
              <Accordion type="single" collapsible className="space-y-4">
                {faqs.map((faq, index) => (
                  <AccordionItem
                    key={index}
                    value={`item-${index}`}
                    className="border border-gray-200 dark:border-white/10 rounded-2xl px-6 py-2 bg-white/50 dark:bg-white/5 backdrop-blur-sm hover:bg-white/70 dark:hover:bg-white/10 transition-all duration-300"
                  >
                    <AccordionTrigger className="text-left hover:no-underline group">
                      <span className="text-lg md:text-xl font-semibold text-gray-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-300">
                        {faq.question}
                      </span>
                    </AccordionTrigger>
                    <AccordionContent className="pt-4 pb-2">
                      <p className="text-base md:text-lg text-gray-700 dark:text-white/70 leading-relaxed">
                        {faq.answer}
                      </p>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          </div>
        </div>

        {/* Contact Support */}
        <div className="mt-20 md:mt-28 text-center">
          <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20 backdrop-blur-sm border border-purple-200 dark:border-purple-400/20 rounded-3xl p-8 md:p-12 shadow-xl relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 dark:from-purple-500/10 dark:to-pink-500/10" />

            <div className="relative z-10">
              <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center mx-auto mb-6 shadow-lg">
                <MessageCircle className="w-8 h-8 text-white" />
              </div>

              <h3 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
                Still have questions?
              </h3>
              <p className="text-lg text-gray-700 dark:text-white/70 mb-8 max-w-2xl mx-auto">
                Our support team is available 24/7 to help you with any questions about our funding process, trading
                rules, or platform features.
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Button className="rounded-full bg-gradient-to-r from-purple-600 to-pink-600 dark:from-purple-500 dark:to-pink-500 text-white hover:from-purple-700 hover:to-pink-700 dark:hover:from-purple-600 dark:hover:to-pink-600 px-8 py-4 text-lg hover:scale-105 transition-all duration-300 hover:shadow-lg">
                  Contact Support
                  <MessageCircle className="ml-2 h-5 w-5" />
                </Button>
                <Button
                  variant="ghost"
                  className="rounded-full text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 hover:bg-purple-50 dark:hover:bg-purple-500/10 px-8 py-4 text-lg transition-all duration-300"
                >
                  Live Chat
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
