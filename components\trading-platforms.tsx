"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Monitor, Download, Star } from "lucide-react"

export default function TradingPlatforms() {
  const platforms = [
    {
      name: "MetaTrader 4",
      shortName: "MT4",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/e/e8/MetaTrader_4_Logo.png/200px-MetaTrader_4_Logo.png",
      description:
        "Industry-standard platform trusted by millions of traders worldwide. Perfect for forex trading with robust EA support.",
      features: [
        "Advanced Charting & Analysis",
        "Expert Advisors (EAs) Supported",
        "Custom Indicators & Scripts",
        "One-Click Trading Execution",
        "Mobile & Desktop Sync",
        "Real-time Market Data",
      ],
      devices: ["Windows", "Mac", "iOS", "Android", "Web"],
      color: "blue",
      popular: false,
      specs: {
        execution: "< 50ms",
        spreads: "From 0.0 pips",
        uptime: "99.9%"
      }
    },
    {
      name: "MetaTrader 5",
      shortName: "MT5",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/8/8c/MetaTrader_5_Logo.png/200px-MetaTrader_5_Logo.png",
      description: "Next-generation multi-asset platform with enhanced order management and institutional-grade features.",
      features: [
        "21 Timeframes & Advanced Charts",
        "Level II Market Depth",
        "Integrated Economic Calendar",
        "Hedging & Netting Modes",
        "Multi-Asset Trading Support",
        "Built-in Trading Signals",
      ],
      devices: ["Windows", "Mac", "iOS", "Android", "Web"],
      color: "green",
      popular: true,
      specs: {
        execution: "< 30ms",
        spreads: "From 0.0 pips",
        uptime: "99.95%"
      }
    },
    {
      name: "cTrader",
      shortName: "cTrader",
      logo: "/placeholder.svg",
      description: "Professional ECN platform with advanced order types, algorithmic trading, and institutional features.",
      features: [
        "ECN Market Access",
        "Advanced Order Types",
        "cBot Algorithmic Trading",
        "Level II Pricing",
        "Professional Charting",
        "Copy Trading Integration",
      ],
      devices: ["Windows", "Mac", "iOS", "Android", "Web"],
      color: "purple",
      popular: false,
      specs: {
        execution: "< 20ms",
        spreads: "Raw ECN",
        uptime: "99.98%"
      }
    },
  ]

  return (
    <section className="py-24 md:py-32 relative" aria-labelledby="platforms-heading">
      <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
        {/* Section Header */}
        <div className="mb-20 md:mb-28 max-w-4xl mx-auto text-center">
          <Badge
            variant="outline"
            className="mb-8 md:mb-12 text-sm font-light border-gray-300 dark:border-white/20 text-gray-600 dark:text-white/80 px-4 py-2 items-center"
          >
            <Monitor className="w-4 h-4 mr-2" />
            Trading Platforms
          </Badge>
          <h2 id="platforms-heading" className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 md:mb-12 leading-tight">
            Trade on{" "}
            <span className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400 bg-clip-text text-transparent">
              industry-leading
            </span>{" "}
            platforms
          </h2>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 leading-relaxed">
            Access MetaTrader 4 and 5 with institutional-grade execution, advanced tools, and seamless multi-device
            trading.
          </p>
        </div>

        {/* Platforms Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 md:gap-16 lg:gap-20">
          {platforms.map((platform, index) => (
            <article key={index} className="relative group">
              <div
                className={`absolute -top-10 -left-10 w-40 h-40 rounded-full bg-gradient-to-r ${
                  platform.color === "green"
                    ? "from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20"
                    : "from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20"
                } blur-3xl`}
              />

              <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 md:p-12 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] relative overflow-hidden">
                <div
                  className={`absolute inset-0 bg-gradient-to-br ${
                    platform.color === "green"
                      ? "from-green-500/5 to-emerald-500/5 dark:from-green-500/10 dark:to-emerald-500/10"
                      : "from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10"
                  } opacity-0 group-hover:opacity-100 transition-opacity duration-500`}
                />

                <div className="relative z-10">
                  {/* Platform Header */}
                  <div className="flex items-start justify-between mb-8">
                    <div className="flex items-center gap-4">
                      {/* Real MetaTrader Logo */}
                      <div className="w-16 h-16 rounded-2xl bg-white dark:bg-gray-800 flex items-center justify-center shadow-lg p-2 group-hover:scale-110 transition-transform duration-300">
                        <img
                          src={platform.logo || "/placeholder.svg"}
                          alt={`${platform.name} logo`}
                          className="w-full h-full object-contain"
                          crossOrigin="anonymous"
                        />
                      </div>
                      <div>
                        <h3 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-2">
                          {platform.name}
                        </h3>
                        {platform.popular && (
                          <Badge
                            className={`${
                              platform.color === "green"
                                ? "bg-green-600 dark:bg-green-500"
                                : "bg-blue-600 dark:bg-blue-500"
                            } text-white text-xs`}
                          >
                            <Star className="w-3 h-3 mr-1" />
                            MOST POPULAR
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-lg text-gray-700 dark:text-white/70 mb-8 leading-relaxed">
                    {platform.description}
                  </p>

                  {/* Features Grid */}
                  {/* Device Support */}

                  {/* Download Button */}
                  <Button
                    className={`w-full rounded-full ${
                      platform.color === "green"
                        ? "bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-500 dark:to-emerald-500 hover:from-green-700 hover:to-emerald-700 dark:hover:from-green-600 dark:hover:to-emerald-600"
                        : "bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-500 dark:to-cyan-500 hover:from-blue-700 hover:to-cyan-700 dark:hover:from-blue-600 dark:hover:to-cyan-600"
                    } text-white px-6 py-4 text-lg hover:scale-105 transition-all duration-300 hover:shadow-lg`}
                  >
                    <Download className="mr-2 h-5 w-5" />
                    Download {platform.shortName}
                  </Button>
                </div>
              </div>
            </article>
          ))}
        </div>
      </div>
    </section>
  )
}
