"use client"

import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, TrendingUp, AlertTriangle, Info, Star } from "lucide-react"
import { useState } from "react"

interface EconomicEvent {
  id: string
  time: string
  currency: string
  flag: string
  event: string
  importance: "low" | "medium" | "high"
  actual?: string
  forecast?: string
  previous?: string
  impact: "positive" | "negative" | "neutral"
}

export default function EconomicCalendar() {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split("T")[0])
  const [selectedCurrency, setSelectedCurrency] = useState("all")
  const [selectedImportance, setSelectedImportance] = useState("all")

  // Mock data - In real implementation, this would come from TradingView API
  const [events, setEvents] = useState<EconomicEvent[]>([
    {
      id: "1",
      time: "08:30",
      currency: "USD",
      flag: "🇺🇸",
      event: "Non-Farm Payrolls",
      importance: "high",
      actual: "263K",
      forecast: "200K",
      previous: "315K",
      impact: "positive",
    },
    {
      id: "2",
      time: "10:00",
      currency: "USD",
      flag: "🇺🇸",
      event: "Unemployment Rate",
      importance: "high",
      actual: "3.7%",
      forecast: "3.6%",
      previous: "3.5%",
      impact: "negative",
    },
    {
      id: "3",
      time: "09:00",
      currency: "EUR",
      flag: "🇪🇺",
      event: "ECB Interest Rate Decision",
      importance: "high",
      forecast: "4.50%",
      previous: "4.25%",
      impact: "neutral",
    },
    {
      id: "4",
      time: "12:30",
      currency: "GBP",
      flag: "🇬🇧",
      event: "GDP Growth Rate",
      importance: "medium",
      actual: "0.2%",
      forecast: "0.1%",
      previous: "0.4%",
      impact: "positive",
    },
    {
      id: "5",
      time: "14:00",
      currency: "CAD",
      flag: "🇨🇦",
      event: "Bank of Canada Rate Decision",
      importance: "medium",
      forecast: "5.00%",
      previous: "5.00%",
      impact: "neutral",
    },
    {
      id: "6",
      time: "15:30",
      currency: "JPY",
      flag: "🇯🇵",
      event: "Industrial Production",
      importance: "low",
      actual: "-0.1%",
      forecast: "0.2%",
      previous: "0.8%",
      impact: "negative",
    },
  ])

  const currencies = ["all", "USD", "EUR", "GBP", "JPY", "CAD", "AUD", "CHF"]
  const importanceLevels = ["all", "high", "medium", "low"]

  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case "high":
        return "bg-red-500"
      case "medium":
        return "bg-yellow-500"
      case "low":
        return "bg-green-500"
      default:
        return "bg-gray-500"
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case "positive":
        return "text-green-600 dark:text-green-400"
      case "negative":
        return "text-red-600 dark:text-red-400"
      default:
        return "text-gray-600 dark:text-gray-400"
    }
  }

  const getImpactIcon = (impact: string) => {
    switch (impact) {
      case "positive":
        return "↗"
      case "negative":
        return "↘"
      default:
        return "→"
    }
  }

  const filteredEvents = events.filter((event) => {
    const currencyMatch = selectedCurrency === "all" || event.currency === selectedCurrency
    const importanceMatch = selectedImportance === "all" || event.importance === selectedImportance
    return currencyMatch && importanceMatch
  })

  return (
    <section className="py-24 md:py-32 relative" aria-labelledby="calendar-heading">
      <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
        {/* Section Header */}
        <div className="mb-20 md:mb-28 max-w-4xl mx-auto text-center">
          <Badge
            variant="outline"
            className="mb-8 md:mb-12 text-sm font-light border-gray-300 dark:border-white/20 text-gray-600 dark:text-white/80 px-4 py-2 items-center"
          >
            <Calendar className="w-4 h-4 mr-2" />
            Economic Calendar
          </Badge>
          <h2 id="calendar-heading" className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 md:mb-12 leading-tight">
            Stay ahead with{" "}
            <span className="bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-400 dark:to-cyan-400 bg-clip-text text-transparent">
              market events
            </span>
          </h2>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 leading-relaxed">
            Track important economic events and news that impact the forex market. Plan your trades around key
            announcements.
          </p>
        </div>

        {/* Filters */}
        <div className="mb-12 md:mb-16">
          <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-6 md:p-8 shadow-xl">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Date Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-white/70">Date</label>
                <input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="w-full rounded-xl border border-gray-200 dark:border-white/20 bg-white/50 dark:bg-white/5 px-4 py-3 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Currency Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-white/70">Currency</label>
                <select
                  value={selectedCurrency}
                  onChange={(e) => setSelectedCurrency(e.target.value)}
                  className="w-full rounded-xl border border-gray-200 dark:border-white/20 bg-white/50 dark:bg-white/5 px-4 py-3 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {currencies.map((currency) => (
                    <option key={currency} value={currency}>
                      {currency === "all" ? "All Currencies" : currency}
                    </option>
                  ))}
                </select>
              </div>

              {/* Importance Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-white/70">Importance</label>
                <select
                  value={selectedImportance}
                  onChange={(e) => setSelectedImportance(e.target.value)}
                  className="w-full rounded-xl border border-gray-200 dark:border-white/20 bg-white/50 dark:bg-white/5 px-4 py-3 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {importanceLevels.map((level) => (
                    <option key={level} value={level}>
                      {level === "all" ? "All Levels" : level.charAt(0).toUpperCase() + level.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Legend */}
        <div className="mb-8 md:mb-12">
          <div className="flex flex-wrap items-center justify-center gap-6">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <span className="text-sm text-gray-600 dark:text-white/60">High Impact</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              <span className="text-sm text-gray-600 dark:text-white/60">Medium Impact</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
              <span className="text-sm text-gray-600 dark:text-white/60">Low Impact</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-green-600 dark:text-green-400">↗</span>
              <span className="text-sm text-gray-600 dark:text-white/60">Better than expected</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-red-600 dark:text-red-400">↘</span>
              <span className="text-sm text-gray-600 dark:text-white/60">Worse than expected</span>
            </div>
          </div>
        </div>

        {/* Events List */}
        <div className="space-y-4">
          {filteredEvents.map((event, index) => (
            <div
              key={event.id}
              className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.01] group"
            >
              <div className="grid grid-cols-1 md:grid-cols-12 gap-4 items-center">
                {/* Time */}
                <div className="md:col-span-2 flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${getImportanceColor(event.importance)}`}></div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-gray-500 dark:text-white/50" />
                    <span className="text-sm font-medium text-gray-900 dark:text-white">{event.time}</span>
                  </div>
                </div>

                {/* Currency */}
                <div className="md:col-span-1 flex items-center gap-2">
                  <span className="text-2xl">{event.flag}</span>
                  <span className="text-sm font-bold text-gray-900 dark:text-white">{event.currency}</span>
                </div>

                {/* Event */}
                <div className="md:col-span-4">
                  <h4 className="text-base font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">
                    {event.event}
                  </h4>
                  <div className="flex items-center gap-2 mt-1">
                    {event.importance === "high" && (
                      <Badge className="bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 text-xs">
                        <AlertTriangle className="w-3 h-3 mr-1" />
                        High Impact
                      </Badge>
                    )}
                    {event.importance === "medium" && (
                      <Badge className="bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400 text-xs">
                        <Info className="w-3 h-3 mr-1" />
                        Medium Impact
                      </Badge>
                    )}
                    {event.importance === "low" && (
                      <Badge className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 text-xs">
                        Low Impact
                      </Badge>
                    )}
                  </div>
                </div>

                {/* Data */}
                <div className="md:col-span-5 grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-xs text-gray-500 dark:text-white/50 mb-1">Actual</div>
                    <div
                      className={`text-sm font-bold ${event.actual ? getImpactColor(event.impact) : "text-gray-400 dark:text-white/40"}`}
                    >
                      {event.actual ? (
                        <span className="flex items-center justify-center gap-1">
                          {event.actual}
                          <span className="text-xs">{getImpactIcon(event.impact)}</span>
                        </span>
                      ) : (
                        "--"
                      )}
                    </div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 dark:text-white/50 mb-1">Forecast</div>
                    <div className="text-sm font-medium text-gray-700 dark:text-white/70">{event.forecast || "--"}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500 dark:text-white/50 mb-1">Previous</div>
                    <div className="text-sm font-medium text-gray-700 dark:text-white/70">{event.previous || "--"}</div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Market Impact Info */}
        <div className="mt-16 md:mt-20">
          <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-3xl p-8 md:p-12 shadow-xl relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5 dark:from-blue-500/10 dark:to-cyan-500/10" />
            <div className="relative z-10">
              <div className="text-center mb-8">
                <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <TrendingUp className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-4">
                  Understanding Market Impact
                </h3>
                <p className="text-lg text-gray-700 dark:text-white/70 max-w-3xl mx-auto">
                  Economic events can significantly impact currency prices. Use this calendar to plan your trades and
                  manage risk effectively.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 rounded-xl bg-red-500/20 flex items-center justify-center mx-auto mb-4">
                    <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
                  </div>
                  <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">High Impact Events</h4>
                  <p className="text-sm text-gray-600 dark:text-white/60">
                    Can cause significant price movements. Exercise extra caution when trading.
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 rounded-xl bg-yellow-500/20 flex items-center justify-center mx-auto mb-4">
                    <Info className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
                  </div>
                  <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">Medium Impact Events</h4>
                  <p className="text-sm text-gray-600 dark:text-white/60">
                    May cause moderate price movements. Monitor closely for trading opportunities.
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 rounded-xl bg-green-500/20 flex items-center justify-center mx-auto mb-4">
                    <Star className="w-6 h-6 text-green-600 dark:text-green-400" />
                  </div>
                  <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">Low Impact Events</h4>
                  <p className="text-sm text-gray-600 dark:text-white/60">
                    Usually have minimal market impact. Good for normal trading conditions.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
