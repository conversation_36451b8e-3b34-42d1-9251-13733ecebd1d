"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import TradingSymbols from "@/components/trading-symbols"

export default function TradingSymbolsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 dark:from-slate-950 dark:via-gray-900 dark:to-slate-950 text-gray-900 dark:text-white">
      {/* Background Effects */}
      <div className="fixed inset-0 bg-[radial-gradient(ellipse_at_center,rgba(34,197,94,0.05),rgba(255,255,255,0))] dark:bg-[radial-gradient(ellipse_at_center,rgba(34,197,94,0.15),rgba(0,0,0,0))]" />
      <div className="fixed top-0 left-0 w-full h-full">
        <div className="absolute top-[20%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-r from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20 blur-3xl" />
        <div className="absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-r from-yellow-500/10 to-orange-500/10 dark:from-yellow-500/20 dark:to-orange-500/20 blur-3xl" />
      </div>

      <div className="relative z-10">
        {/* Navigation */}
        <nav className="p-8">
          <Link href="/">
            <Button
              variant="ghost"
              className="text-gray-600 dark:text-white/70 hover:text-gray-900 dark:hover:text-white"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
          </Link>
        </nav>

        {/* Page Content */}
        <TradingSymbols />
      </div>
    </div>
  )
}
