"use client"

import { Badge } from "@/components/ui/badge"
import { Award, Trophy, Star, Medal, Crown, Target } from "lucide-react"

export default function AwardsSection() {
  const awards = [
    {
      title: "Best Proprietary Trading Firm",
      organization: "Global Finance Awards",
      year: "2024",
      icon: Trophy,
      color: "gold",
      description: "Recognized for exceptional trader funding programs and industry-leading profit sharing models.",
    },
    {
      title: "Excellence in Risk Management",
      organization: "Financial Technology Institute",
      year: "2024",
      icon: Star,
      color: "blue",
      description: "Awarded for implementing advanced risk management systems and trader protection protocols.",
    },
    {
      title: "Innovation in Trading Technology",
      organization: "FinTech Innovation Awards",
      year: "2023",
      icon: Target,
      color: "green",
      description: "Leading innovation in proprietary trading platforms and algorithmic execution systems.",
    },
    {
      title: "Outstanding Trading Environment",
      organization: "Professional Traders Association",
      year: "2023",
      icon: Medal,
      color: "purple",
      description: "Superior execution speeds, competitive spreads, and institutional-grade trading infrastructure.",
    },
    {
      title: "Fastest Growing Capital Provider",
      organization: "Alternative Investment Review",
      year: "2023",
      icon: Crown,
      color: "orange",
      description: "Exceptional growth in funded trader base and capital deployment across global markets.",
    },
    {
      title: "Trader Development Excellence",
      organization: "Education in Finance Awards",
      year: "2022",
      icon: Award,
      color: "teal",
      description: "Comprehensive educational programs and mentorship initiatives for developing trading professionals.",
    },
  ]

  const getColorClasses = (color: string) => {
    const colorMap = {
      gold: {
        bg: "from-yellow-500/10 to-orange-500/10 dark:from-yellow-500/20 dark:to-orange-500/20",
        border: "border-yellow-200 dark:border-yellow-400/20",
        icon: "text-yellow-600 dark:text-yellow-400",
        iconBg: "from-yellow-500 to-orange-500",
        blur: "from-yellow-500/10 to-orange-500/10 dark:from-yellow-500/20 dark:to-orange-500/20",
      },
      blue: {
        bg: "from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20",
        border: "border-blue-200 dark:border-blue-400/20",
        icon: "text-blue-600 dark:text-blue-400",
        iconBg: "from-blue-500 to-cyan-500",
        blur: "from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20",
      },
      green: {
        bg: "from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20",
        border: "border-green-200 dark:border-green-400/20",
        icon: "text-green-600 dark:text-green-400",
        iconBg: "from-green-500 to-emerald-500",
        blur: "from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20",
      },
      purple: {
        bg: "from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20",
        border: "border-purple-200 dark:border-purple-400/20",
        icon: "text-purple-600 dark:text-purple-400",
        iconBg: "from-purple-500 to-pink-500",
        blur: "from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20",
      },
      orange: {
        bg: "from-orange-500/10 to-red-500/10 dark:from-orange-500/20 dark:to-red-500/20",
        border: "border-orange-200 dark:border-orange-400/20",
        icon: "text-orange-600 dark:text-orange-400",
        iconBg: "from-orange-500 to-red-500",
        blur: "from-orange-500/10 to-red-500/10 dark:from-orange-500/20 dark:to-red-500/20",
      },
      teal: {
        bg: "from-teal-500/10 to-cyan-500/10 dark:from-teal-500/20 dark:to-cyan-500/20",
        border: "border-teal-200 dark:border-teal-400/20",
        icon: "text-teal-600 dark:text-teal-400",
        iconBg: "from-teal-500 to-cyan-500",
        blur: "from-teal-500/10 to-cyan-500/10 dark:from-teal-500/20 dark:to-cyan-500/20",
      },
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.gold
  }

  return (
    <section className="py-24 md:py-32 relative" aria-labelledby="awards-heading">
      <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
        {/* Section Header */}
        <div className="mb-20 md:mb-28 max-w-4xl mx-auto text-center">
          <Badge
            variant="outline"
            className="mb-8 md:mb-12 text-sm font-light border-gray-300 dark:border-white/20 text-gray-600 dark:text-white/80 px-4 py-2 items-center"
          >
            <Award className="w-4 h-4 mr-2" />
            Industry Recognition
          </Badge>
          <h2 id="awards-heading" className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 md:mb-12 leading-tight">
            <span className="bg-gradient-to-r from-yellow-600 to-orange-600 dark:from-yellow-400 dark:to-orange-400 bg-clip-text text-transparent">
              Award-winning
            </span>{" "}
            excellence
          </h2>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 leading-relaxed">
            Recognized by industry leaders for our commitment to trader success, innovation, and exceptional service.
          </p>
        </div>

        {/* Awards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 md:gap-12">
          {awards.map((award, index) => {
            const colors = getColorClasses(award.color)
            const IconComponent = award.icon

            return (
              <article key={index} className="relative group">
                <div
                  className={`absolute -top-5 -left-5 w-32 h-32 rounded-full bg-gradient-to-r ${colors.blur} blur-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500`}
                />

                <div
                  className={`bg-gradient-to-br ${colors.bg} backdrop-blur-sm border ${colors.border} rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] relative overflow-hidden h-full`}
                >
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${colors.bg} opacity-0 group-hover:opacity-100 transition-opacity duration-500`}
                  />

                  <div className="relative z-10 h-full flex flex-col">
                    {/* Award Icon */}
                    <div className="mb-6">
                      <div
                        className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${colors.iconBg} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}
                      >
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                    </div>

                    {/* Award Content */}
                    <div className="flex-grow">
                      <div className="mb-4">
                        <Badge className={`${colors.icon} bg-transparent border ${colors.border} text-xs mb-2`}>
                          {award.year}
                        </Badge>
                        <h3 className="text-xl md:text-2xl font-bold text-gray-900 dark:text-white mb-2 leading-tight">
                          {award.title}
                        </h3>
                        <p className={`text-sm font-medium ${colors.icon} mb-4`}>{award.organization}</p>
                      </div>

                      <p className="text-sm md:text-base text-gray-700 dark:text-white/70 leading-relaxed">
                        {award.description}
                      </p>
                    </div>

                    {/* Decorative Elements */}
                    <div className="absolute top-4 right-4 opacity-20 group-hover:opacity-40 transition-opacity duration-300">
                      <div
                        className={`w-8 h-8 rounded-lg bg-gradient-to-r ${colors.iconBg} flex items-center justify-center`}
                      >
                        <Star className="w-4 h-4 text-white" />
                      </div>
                    </div>
                  </div>
                </div>
              </article>
            )
          })}
        </div>

        {/* Stats Section */}
        <div className="mt-20 md:mt-28">
          <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 md:p-12 shadow-xl relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/5 to-orange-500/5 dark:from-yellow-500/10 dark:to-orange-500/10" />
            <div className="relative z-10">
              <div className="text-center mb-12">
                <h3 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                  Trusted by Industry Leaders
                </h3>
                <p className="text-lg text-gray-700 dark:text-white/70">
                  Our commitment to excellence is recognized across the trading industry
                </p>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-yellow-600 dark:text-yellow-400 mb-2">15+</div>
                  <div className="text-sm text-gray-600 dark:text-white/60">Industry Awards</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-green-600 dark:text-green-400 mb-2">98%</div>
                  <div className="text-sm text-gray-600 dark:text-white/60">Trader Satisfaction</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2">50K+</div>
                  <div className="text-sm text-gray-600 dark:text-white/60">Active Traders</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl md:text-4xl font-bold text-purple-600 dark:text-purple-400 mb-2">$50M+</div>
                  <div className="text-sm text-gray-600 dark:text-white/60">Paid to Traders</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
