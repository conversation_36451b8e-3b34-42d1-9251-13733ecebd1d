"use client"

import { Badge } from "@/components/ui/badge"
import { <PERSON>ertTriangle, Info, Shield, FileText } from "lucide-react"

export default function RiskDisclosure() {
  const riskPoints = [
    "Trading involves substantial risk and may not be suitable for all investors.",
    "Past performance is not indicative of future results.",
    "You should carefully consider whether trading is appropriate for you in light of your experience, objectives, financial resources, and other relevant circumstances.",
    "All trading involves risk, and losses can exceed deposits.",
    "The high degree of leverage can work against you as well as for you.",
    "Before deciding to trade, you should carefully consider your investment objectives, level of experience, and risk appetite.",
    "You should be aware of all the risks associated with trading and seek advice from an independent financial advisor if you have any doubts.",
  ]

  const importantNotes = [
    {
      title: "Demo Trading Environment",
      description: "All evaluation and funded accounts operate in a simulated trading environment. No real money is at risk during the evaluation process.",
      icon: Info,
    },
    {
      title: "Educational Purpose",
      description: "Our services are designed for educational purposes to help traders develop and demonstrate their skills in a risk-controlled environment.",
      icon: FileText,
    },
    {
      title: "Performance Disclaimer",
      description: "Individual results may vary. Success in our evaluation process does not guarantee future trading success or profitability.",
      icon: Shield,
    },
  ]

  return (
    <section className="py-16 md:py-20 bg-gray-50 dark:bg-gray-900/50 relative" aria-labelledby="risk-heading">
      <div className="max-w-4xl mx-auto px-8 md:px-12 lg:px-16">
        {/* Section Header */}
        <div className="mb-12 text-center">
          <Badge
            variant="outline"
            className="mb-6 text-sm font-light border-orange-300 dark:border-orange-400/20 text-orange-600 dark:text-orange-400 px-4 py-2 items-center"
          >
            <AlertTriangle className="w-4 h-4 mr-2" />
            Risk Disclosure
          </Badge>
          <h2 id="risk-heading" className="text-3xl md:text-4xl font-bold mb-6 leading-tight text-gray-900 dark:text-white">
            Important Risk Information
          </h2>
          <p className="text-lg text-gray-700 dark:text-white/70 leading-relaxed">
            Please read and understand the following risk disclosure before participating in our trading programs.
          </p>
        </div>

        {/* Important Notes */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          {importantNotes.map((note, index) => {
            const IconComponent = note.icon
            return (
              <div
                key={index}
                className="bg-white dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-2xl p-6 text-center"
              >
                <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-500/20 to-cyan-500/20 dark:from-blue-500/30 dark:to-cyan-500/30 flex items-center justify-center mx-auto mb-4">
                  <IconComponent className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                  {note.title}
                </h3>
                <p className="text-sm text-gray-600 dark:text-white/60 leading-relaxed">
                  {note.description}
                </p>
              </div>
            )
          })}
        </div>

        {/* Risk Disclosure */}
        <div className="bg-white dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-2xl p-8">
          <div className="flex items-start gap-4 mb-6">
            <div className="w-8 h-8 rounded-lg bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center flex-shrink-0">
              <AlertTriangle className="w-5 h-5 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                Risk Warning
              </h3>
              <div className="space-y-3">
                {riskPoints.map((point, index) => (
                  <p key={index} className="text-sm text-gray-700 dark:text-white/70 leading-relaxed">
                    • {point}
                  </p>
                ))}
              </div>
            </div>
          </div>

          <div className="border-t border-gray-200 dark:border-white/10 pt-6 mt-6">
            <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
              Regulatory Information
            </h4>
            <p className="text-sm text-gray-600 dark:text-white/60 leading-relaxed mb-4">
              Apex Capital operates as a proprietary trading firm providing simulated trading environments for educational and evaluation purposes. We are not a licensed investment advisor or broker-dealer. Our services do not constitute investment advice or recommendations.
            </p>
            <p className="text-sm text-gray-600 dark:text-white/60 leading-relaxed mb-4">
              All trading activities conducted through our platform are simulated and for educational purposes only. Participants do not trade with real money during the evaluation process, and all funded accounts operate in a demo environment.
            </p>
            <p className="text-sm text-gray-600 dark:text-white/60 leading-relaxed">
              By participating in our programs, you acknowledge that you have read, understood, and agree to these terms and the associated risks.
            </p>
          </div>
        </div>

        {/* Contact Information */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-600 dark:text-white/60">
            If you have questions about these risks or our services, please contact our support team at{" "}
            <a href="mailto:<EMAIL>" className="text-blue-600 dark:text-blue-400 hover:underline">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </section>
  )
}
