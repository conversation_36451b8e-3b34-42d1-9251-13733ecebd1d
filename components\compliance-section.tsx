"use client"

import { Badge } from "@/components/ui/badge"
import { Shield, Award, CheckCircle, Lock, Globe, FileText, Users, TrendingUp } from "lucide-react"

export default function ComplianceSection() {
  const certifications = [
    {
      title: "Financial Conduct Authority",
      subtitle: "FCA Regulated",
      code: "FRN: 123456",
      icon: Shield,
      color: "blue",
      description: "Authorized and regulated by the UK's Financial Conduct Authority",
    },
    {
      title: "Securities Commission",
      subtitle: "SEC Compliant",
      code: "CRD: 789012",
      icon: Award,
      color: "green",
      description: "Registered with the Securities and Exchange Commission",
    },
    {
      title: "ISO 27001 Certified",
      subtitle: "Information Security",
      code: "ISO/IEC 27001:2013",
      icon: Lock,
      color: "purple",
      description: "International standard for information security management",
    },
    {
      title: "CFTC Registration",
      subtitle: "Commodity Trading",
      code: "NFA ID: 345678",
      icon: FileText,
      color: "orange",
      description: "Registered with the Commodity Futures Trading Commission",
    },
  ]

  const complianceFeatures = [
    {
      title: "Segregated Client Funds",
      description: "All client funds are held in segregated accounts with tier-1 banks",
      icon: Shield,
      color: "text-blue-600 dark:text-blue-400",
    },
    {
      title: "Regular Audits",
      description: "Independent third-party audits conducted quarterly",
      icon: CheckCircle,
      color: "text-green-600 dark:text-green-400",
    },
    {
      title: "Risk Management",
      description: "Advanced risk management systems monitor all trading activity",
      icon: TrendingUp,
      color: "text-purple-600 dark:text-purple-400",
    },
    {
      title: "Data Protection",
      description: "Bank-level encryption and security protocols protect your data",
      icon: Lock,
      color: "text-orange-600 dark:text-orange-400",
    },
    {
      title: "Global Compliance",
      description: "Operating in compliance with international financial regulations",
      icon: Globe,
      color: "text-teal-600 dark:text-teal-400",
    },
    {
      title: "Transparent Operations",
      description: "Full transparency in all trading conditions and fee structures",
      icon: FileText,
      color: "text-red-600 dark:text-red-400",
    },
  ]

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: {
        bg: "from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20",
        border: "border-blue-200 dark:border-blue-400/20",
        icon: "text-blue-600 dark:text-blue-400",
        iconBg: "from-blue-500 to-cyan-500",
      },
      green: {
        bg: "from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20",
        border: "border-green-200 dark:border-green-400/20",
        icon: "text-green-600 dark:text-green-400",
        iconBg: "from-green-500 to-emerald-500",
      },
      purple: {
        bg: "from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20",
        border: "border-purple-200 dark:border-purple-400/20",
        icon: "text-purple-600 dark:text-purple-400",
        iconBg: "from-purple-500 to-pink-500",
      },
      orange: {
        bg: "from-orange-500/10 to-red-500/10 dark:from-orange-500/20 dark:to-red-500/20",
        border: "border-orange-200 dark:border-orange-400/20",
        icon: "text-orange-600 dark:text-orange-400",
        iconBg: "from-orange-500 to-red-500",
      },
    }
    return colorMap[color as keyof typeof colorMap] || colorMap.blue
  }

  return (
    <section className="py-24 md:py-32 relative" aria-labelledby="compliance-heading">
      <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
        {/* Section Header */}
        <div className="mb-20 md:mb-28 max-w-4xl mx-auto text-center">
          <Badge
            variant="outline"
            className="mb-8 md:mb-12 text-sm font-light border-gray-300 dark:border-white/20 text-gray-600 dark:text-white/80 px-4 py-2 items-center"
          >
            <Shield className="w-4 h-4 mr-2" />
            Regulatory Compliance
          </Badge>
          <h2 id="compliance-heading" className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 md:mb-12 leading-tight">
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400 bg-clip-text text-transparent">
              Regulated
            </span>{" "}
            and secure
          </h2>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 leading-relaxed">
            Your capital and data are protected by the highest industry standards and regulatory oversight.
          </p>
        </div>

        {/* Certifications Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {certifications.map((cert, index) => {
            const colors = getColorClasses(cert.color)
            const IconComponent = cert.icon

            return (
              <div key={index} className="relative group">
                <div
                  className={`bg-gradient-to-br ${colors.bg} backdrop-blur-sm border ${colors.border} rounded-3xl p-6 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] relative overflow-hidden h-full`}
                >
                  <div className="relative z-10 text-center">
                    {/* Certification Icon */}
                    <div className="mb-6">
                      <div
                        className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${colors.iconBg} flex items-center justify-center shadow-lg mx-auto group-hover:scale-110 transition-transform duration-300`}
                      >
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                    </div>

                    {/* Certification Content */}
                    <div className="mb-4">
                      <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2 leading-tight">
                        {cert.title}
                      </h3>
                      <p className={`text-sm font-medium ${colors.icon} mb-2`}>{cert.subtitle}</p>
                      <Badge className={`${colors.icon} bg-transparent border ${colors.border} text-xs`}>
                        {cert.code}
                      </Badge>
                    </div>

                    <p className="text-sm text-gray-700 dark:text-white/70 leading-relaxed">
                      {cert.description}
                    </p>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Compliance Features */}
        <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 md:p-12 shadow-xl relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 dark:from-blue-500/10 dark:to-purple-500/10" />
          
          <div className="relative z-10">
            <div className="text-center mb-12">
              <h3 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Your Security is Our Priority
              </h3>
              <p className="text-lg text-gray-700 dark:text-white/70">
                We maintain the highest standards of security and compliance to protect your interests
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {complianceFeatures.map((feature, index) => {
                const IconComponent = feature.icon
                return (
                  <div key={index} className="flex items-start gap-4 group">
                    <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-gray-100 to-gray-200 dark:from-white/10 dark:to-white/20 flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                      <IconComponent className={`w-6 h-6 ${feature.color}`} />
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                        {feature.title}
                      </h4>
                      <p className="text-sm text-gray-700 dark:text-white/70 leading-relaxed">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="mt-20 text-center">
          <div className="inline-flex items-center gap-8 bg-gradient-to-r from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20 backdrop-blur-sm border border-green-200 dark:border-green-400/20 rounded-2xl px-8 py-6">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center">
                <Shield className="w-6 h-6 text-white" />
              </div>
              <div className="text-left">
                <div className="text-lg font-bold text-gray-900 dark:text-white">$100M+</div>
                <div className="text-sm text-gray-600 dark:text-white/60">Insured Capital</div>
              </div>
            </div>
            <div className="w-px h-12 bg-gray-300 dark:bg-white/20"></div>
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-500 to-cyan-500 flex items-center justify-center">
                <Users className="w-6 h-6 text-white" />
              </div>
              <div className="text-left">
                <div className="text-lg font-bold text-gray-900 dark:text-white">50K+</div>
                <div className="text-sm text-gray-600 dark:text-white/60">Protected Traders</div>
              </div>
            </div>
            <div className="w-px h-12 bg-gray-300 dark:bg-white/20"></div>
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center">
                <Award className="w-6 h-6 text-white" />
              </div>
              <div className="text-left">
                <div className="text-lg font-bold text-gray-900 dark:text-white">AAA</div>
                <div className="text-sm text-gray-600 dark:text-white/60">Security Rating</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
