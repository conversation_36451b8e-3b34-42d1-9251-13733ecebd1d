"use client"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Check, X, Star, TrendingUp, Shield, Zap } from "lucide-react"

export default function CompareSection() {
  const features = [
    {
      feature: "Maximum Funding",
      apex: "$200,000",
      competitor1: "$100,000",
      competitor2: "$150,000",
      highlight: true,
    },
    {
      feature: "Profit Split",
      apex: "Up to 90%",
      competitor1: "80%",
      competitor2: "85%",
      highlight: true,
    },
    {
      feature: "Evaluation Fee",
      apex: "$99",
      competitor1: "$149",
      competitor2: "$129",
      highlight: true,
    },
    {
      feature: "Daily Drawdown",
      apex: "5%",
      competitor1: "4%",
      competitor2: "5%",
      highlight: false,
    },
    {
      feature: "Max Drawdown",
      apex: "10%",
      competitor1: "8%",
      competitor2: "10%",
      highlight: false,
    },
    {
      feature: "Minimum Trading Days",
      apex: "5 days",
      competitor1: "10 days",
      competitor2: "7 days",
      highlight: true,
    },
    {
      feature: "Profit Target",
      apex: "8%",
      competitor1: "10%",
      competitor2: "8%",
      highlight: false,
    },
    {
      feature: "News Trading",
      apex: true,
      competitor1: false,
      competitor2: true,
      highlight: true,
    },
    {
      feature: "Weekend Holding",
      apex: true,
      competitor1: false,
      competitor2: false,
      highlight: true,
    },
    {
      feature: "Expert Advisors",
      apex: true,
      competitor1: true,
      competitor2: false,
      highlight: false,
    },
  ]

  return (
    <section className="py-24 md:py-32 relative" aria-labelledby="compare-heading">
      <div className="max-w-7xl mx-auto px-8 md:px-12 lg:px-16">
        {/* Section Header */}
        <div className="mb-20 md:mb-28 max-w-4xl mx-auto text-center">
          <Badge
            variant="outline"
            className="mb-8 md:mb-12 text-sm font-light border-gray-300 dark:border-white/20 text-gray-600 dark:text-white/80 px-4 py-2 items-center"
          >
            <TrendingUp className="w-4 h-4 mr-2" />
            Industry Comparison
          </Badge>
          <h2 id="compare-heading" className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 md:mb-12 leading-tight">
            Why{" "}
            <span className="bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-400 dark:to-emerald-400 bg-clip-text text-transparent">
              Apex Capital
            </span>{" "}
            leads
          </h2>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 leading-relaxed">
            Compare our industry-leading terms with other prop trading firms. See why thousands choose Apex Capital.
          </p>
        </div>

        {/* Comparison Table */}
        <div className="relative">
          <div className="absolute -top-10 -left-10 w-40 h-40 rounded-full bg-gradient-to-r from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20 blur-3xl" />
          <div className="absolute -bottom-10 -right-10 w-40 h-40 rounded-full bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 blur-3xl" />

          <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 md:p-12 shadow-xl overflow-hidden relative">
            <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 dark:from-green-500/10 dark:to-emerald-500/10" />

            <div className="relative z-10">
              {/* Table Header */}
              <div className="grid grid-cols-4 gap-4 md:gap-8 mb-8 md:mb-12">
                <div className="text-left">
                  <h3 className="text-lg md:text-xl font-bold text-gray-900 dark:text-white">Features</h3>
                </div>
                <div className="text-center relative">
                  <div className="absolute -top-4 -left-4 -right-4 -bottom-4 bg-gradient-to-r from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20 rounded-2xl border-2 border-green-300/50 dark:border-green-400/30" />
                  <div className="relative">
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <Star className="w-5 h-5 text-green-600 dark:text-green-400" />
                      <h3 className="text-lg md:text-xl font-bold text-green-600 dark:text-green-400">Apex Capital</h3>
                    </div>
                    <Badge className="bg-green-600 dark:bg-green-500 text-white text-xs">BEST VALUE</Badge>
                  </div>
                </div>
                <div className="text-center">
                  <h3 className="text-lg md:text-xl font-bold text-gray-600 dark:text-white/60">Competitor A</h3>
                </div>
                <div className="text-center">
                  <h3 className="text-lg md:text-xl font-bold text-gray-600 dark:text-white/60">Competitor B</h3>
                </div>
              </div>

              {/* Table Rows */}
              <div className="space-y-4">
                {features.map((item, index) => (
                  <div
                    key={index}
                    className={`grid grid-cols-4 gap-4 md:gap-8 py-4 md:py-6 px-4 md:px-6 rounded-2xl transition-all duration-300 hover:bg-gray-50/50 dark:hover:bg-white/5 ${
                      item.highlight
                        ? "bg-green-50/50 dark:bg-green-500/5 border border-green-200/50 dark:border-green-400/20"
                        : ""
                    }`}
                  >
                    <div className="flex items-center">
                      <span className="text-sm md:text-base font-medium text-gray-900 dark:text-white">
                        {item.feature}
                      </span>
                      {item.highlight && <Shield className="w-4 h-4 ml-2 text-green-600 dark:text-green-400" />}
                    </div>
                    <div className="flex items-center justify-center">
                      {typeof item.apex === "boolean" ? (
                        item.apex ? (
                          <Check className="w-5 h-5 text-green-600 dark:text-green-400" />
                        ) : (
                          <X className="w-5 h-5 text-red-500" />
                        )
                      ) : (
                        <span className="text-sm md:text-base font-bold text-green-600 dark:text-green-400">
                          {item.apex}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center justify-center">
                      {typeof item.competitor1 === "boolean" ? (
                        item.competitor1 ? (
                          <Check className="w-5 h-5 text-gray-400" />
                        ) : (
                          <X className="w-5 h-5 text-red-500" />
                        )
                      ) : (
                        <span className="text-sm md:text-base text-gray-600 dark:text-white/60">
                          {item.competitor1}
                        </span>
                      )}
                    </div>
                    <div className="flex items-center justify-center">
                      {typeof item.competitor2 === "boolean" ? (
                        item.competitor2 ? (
                          <Check className="w-5 h-5 text-gray-400" />
                        ) : (
                          <X className="w-5 h-5 text-red-500" />
                        )
                      ) : (
                        <span className="text-sm md:text-base text-gray-600 dark:text-white/60">
                          {item.competitor2}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* CTA */}
              <div className="mt-12 md:mt-16 text-center">
                <Button className="rounded-full bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-500 dark:to-emerald-500 text-white hover:from-green-700 hover:to-emerald-700 dark:hover:from-green-600 dark:hover:to-emerald-600 px-8 py-4 text-lg hover:scale-105 transition-all duration-300 hover:shadow-lg">
                  Choose Apex Capital
                  <Zap className="ml-2 h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
