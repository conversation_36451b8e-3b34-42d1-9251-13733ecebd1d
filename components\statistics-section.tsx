"use client"

import { Badge } from "@/components/ui/badge"
import { TrendingUp, Users, DollarSign, Globe, Award, Target, Clock, BarChart3 } from "lucide-react"
import { useState, useEffect } from "react"

export default function StatisticsSection() {
  const [counters, setCounters] = useState({
    traders: 0,
    payouts: 0,
    countries: 0,
    success: 0,
  })

  const finalStats = {
    traders: 8500,
    payouts: 12000000,
    countries: 45,
    success: 73,
  }

  useEffect(() => {
    const duration = 2000 // 2 seconds
    const steps = 60
    const interval = duration / steps

    let step = 0
    const timer = setInterval(() => {
      step++
      const progress = step / steps

      setCounters({
        traders: Math.floor(finalStats.traders * progress),
        payouts: Math.floor(finalStats.payouts * progress),
        countries: Math.floor(finalStats.countries * progress),
        success: Math.floor(finalStats.success * progress),
      })

      if (step >= steps) {
        clearInterval(timer)
        setCounters(finalStats)
      }
    }, interval)

    return () => clearInterval(timer)
  }, [])

  const mainStats = [
    {
      value: counters.traders.toLocaleString() + "+",
      label: "Funded Traders",
      description: "Professional traders funded globally",
      icon: Users,
      color: "text-blue-600 dark:text-blue-400",
      bgColor: "from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20",
      borderColor: "border-blue-200 dark:border-blue-400/20",
    },
    {
      value: "$" + (counters.payouts / 1000000).toFixed(0) + "M+",
      label: "Total Payouts",
      description: "Paid to successful traders",
      icon: DollarSign,
      color: "text-green-600 dark:text-green-400",
      bgColor: "from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20",
      borderColor: "border-green-200 dark:border-green-400/20",
    },
    {
      value: counters.countries + "+",
      label: "Countries",
      description: "Global trader community",
      icon: Globe,
      color: "text-purple-600 dark:text-purple-400",
      bgColor: "from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20",
      borderColor: "border-purple-200 dark:border-purple-400/20",
    },
    {
      value: counters.success + "%",
      label: "Success Rate",
      description: "Traders achieving profitability",
      icon: TrendingUp,
      color: "text-orange-600 dark:text-orange-400",
      bgColor: "from-orange-500/10 to-red-500/10 dark:from-orange-500/20 dark:to-red-500/20",
      borderColor: "border-orange-200 dark:border-orange-400/20",
    },
  ]

  const performanceMetrics = [
    {
      metric: "Average Monthly Return",
      value: "8.2%",
      description: "Across all funded accounts",
      icon: BarChart3,
    },
    {
      metric: "Average Funding Time",
      value: "28 days",
      description: "From start to funded account",
      icon: Clock,
    },
    {
      metric: "Top Trader Profit",
      value: "$124K",
      description: "Highest individual earnings",
      icon: Award,
    },
    {
      metric: "Account Scaling Rate",
      value: "42%",
      description: "Traders who scale their accounts",
      icon: Target,
    },
  ]

  return (
    <section className="py-24 md:py-32 relative" aria-labelledby="statistics-heading">
      <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16">
        {/* Section Header */}
        <div className="mb-20 md:mb-28 max-w-4xl mx-auto text-center">
          <Badge
            variant="outline"
            className="mb-8 md:mb-12 text-sm font-light border-gray-300 dark:border-white/20 text-gray-600 dark:text-white/80 px-4 py-2 items-center"
          >
            <BarChart3 className="w-4 h-4 mr-2" />
            Performance Metrics
          </Badge>
          <h2 id="statistics-heading" className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 md:mb-12 leading-tight">
            <span className="bg-gradient-to-r from-green-600 to-blue-600 dark:from-green-400 dark:to-blue-400 bg-clip-text text-transparent">
              Proven
            </span>{" "}
            track record
          </h2>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 leading-relaxed">
            Our numbers speak for themselves. Join a community of successful traders who have built profitable careers with our funding.
          </p>
        </div>

        {/* Main Statistics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {mainStats.map((stat, index) => {
            const IconComponent = stat.icon
            return (
              <div
                key={index}
                className={`bg-gradient-to-br ${stat.bgColor} backdrop-blur-sm border ${stat.borderColor} rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] relative overflow-hidden group`}
              >
                <div className="relative z-10 text-center">
                  {/* Icon */}
                  <div className="mb-6">
                    <div className="w-16 h-16 rounded-2xl bg-white/80 dark:bg-white/10 flex items-center justify-center shadow-lg mx-auto group-hover:scale-110 transition-transform duration-300">
                      <IconComponent className={`w-8 h-8 ${stat.color}`} />
                    </div>
                  </div>

                  {/* Value */}
                  <div className="mb-4">
                    <div className={`text-4xl md:text-5xl font-bold ${stat.color} mb-2`}>
                      {stat.value}
                    </div>
                    <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                      {stat.label}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-white/60">
                      {stat.description}
                    </p>
                  </div>
                </div>

                {/* Hover Effect */}
                <div className={`absolute inset-0 bg-gradient-to-br ${stat.bgColor} opacity-0 group-hover:opacity-100 transition-opacity duration-500`} />
              </div>
            )
          })}
        </div>

        {/* Performance Metrics */}
        <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl p-8 md:p-12 shadow-xl relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-gray-500/5 to-blue-500/5 dark:from-gray-500/10 dark:to-blue-500/10" />
          
          <div className="relative z-10">
            <div className="text-center mb-12">
              <h3 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Performance Insights
              </h3>
              <p className="text-lg text-gray-700 dark:text-white/70">
                Key metrics that demonstrate our commitment to trader success
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {performanceMetrics.map((metric, index) => {
                const IconComponent = metric.icon
                return (
                  <div key={index} className="text-center group">
                    <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-gray-100 to-gray-200 dark:from-white/10 dark:to-white/20 flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                      <IconComponent className="w-8 h-8 text-gray-600 dark:text-white/60" />
                    </div>
                    <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                      {metric.value}
                    </div>
                    <h4 className="text-lg font-bold text-gray-900 dark:text-white mb-2">
                      {metric.metric}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-white/60">
                      {metric.description}
                    </p>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="mt-20 text-center">
          <div className="inline-flex flex-wrap items-center justify-center gap-8 bg-gradient-to-r from-green-500/10 to-blue-500/10 dark:from-green-500/20 dark:to-blue-500/20 backdrop-blur-sm border border-green-200 dark:border-green-400/20 rounded-2xl px-8 py-6">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 rounded-full bg-green-500 animate-pulse"></div>
              <span className="text-sm font-medium text-gray-700 dark:text-white/70">
                Live Trading Data
              </span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 rounded-full bg-blue-500 animate-pulse"></div>
              <span className="text-sm font-medium text-gray-700 dark:text-white/70">
                Real-time Updates
              </span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 rounded-full bg-purple-500 animate-pulse"></div>
              <span className="text-sm font-medium text-gray-700 dark:text-white/70">
                Verified Results
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
