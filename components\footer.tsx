"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  TrendingUp,
  Mail,
  Phone,
  MapPin,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Youtube,
  Globe,
  Shield,
  Award,
  Clock,
  ArrowRight,
  ExternalLink,
} from "lucide-react"
import Link from "next/link"

export default function Footer() {
  const currentYear = new Date().getFullYear()

  const quickLinks = [
    { name: "About Us", href: "/about" },
    { name: "How It Works", href: "/how-it-works" },
    { name: "Pricing", href: "/pricing" },
    { name: "Trading Rules", href: "/rules" },
    { name: "FAQ", href: "/faq" },
    { name: "Contact", href: "/contact" },
  ]

  const tradingLinks = [
    { name: "Trading Platforms", href: "/platforms" },
    { name: "Market Analysis", href: "/analysis" },
    { name: "Economic Calendar", href: "/economic-calendar" },
    { name: "Trading Symbols", href: "/trading-symbols" },
    { name: "Risk Management", href: "/risk-management" },
    { name: "Educational Resources", href: "/education" },
  ]

  const supportLinks = [
    { name: "Help Center", href: "/help" },
    { name: "Live Chat", href: "/chat" },
    { name: "Submit Ticket", href: "/support" },
    { name: "Community Forum", href: "/forum" },
    { name: "Video Tutorials", href: "/tutorials" },
    { name: "Webinars", href: "/webinars" },
  ]

  const legalLinks = [
    { name: "Terms of Service", href: "/terms" },
    { name: "Privacy Policy", href: "/privacy" },
    { name: "Risk Disclosure", href: "/risk-disclosure" },
    { name: "Refund Policy", href: "/refund" },
    { name: "Cookie Policy", href: "/cookies" },
    { name: "Compliance", href: "/compliance" },
  ]

  const socialLinks = [
    { name: "Facebook", icon: Facebook, href: "https://facebook.com/apexcapital" },
    { name: "Twitter", icon: Twitter, href: "https://twitter.com/apexcapital" },
    { name: "Instagram", icon: Instagram, href: "https://instagram.com/apexcapital" },
    { name: "LinkedIn", icon: Linkedin, href: "https://linkedin.com/company/apexcapital" },
    { name: "YouTube", icon: Youtube, href: "https://youtube.com/apexcapital" },
  ]

  const cryptoCurrencies = [
    {
      name: "Bitcoin",
      symbol: "BTC",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/4/46/Bitcoin.svg/64px-Bitcoin.svg.png",
    },
    {
      name: "Ethereum",
      symbol: "ETH",
      logo: "https://upload.wikimedia.org/wikipedia/commons/thumb/6/6f/Ethereum-icon-purple.svg/64px-Ethereum-icon-purple.svg.png",
    },
    {
      name: "Tether",
      symbol: "USDT",
      logo: "https://cryptologos.cc/logos/tether-usdt-logo.png",
    },
    {
      name: "BNB",
      symbol: "BNB",
      logo: "https://cryptologos.cc/logos/bnb-bnb-logo.png",
    },
    {
      name: "Solana",
      symbol: "SOL",
      logo: "https://cryptologos.cc/logos/solana-sol-logo.png",
    },
    {
      name: "XRP",
      symbol: "XRP",
      logo: "https://cryptologos.cc/logos/xrp-xrp-logo.png",
    },
    {
      name: "USDC",
      symbol: "USDC",
      logo: "https://cryptologos.cc/logos/usd-coin-usdc-logo.png",
    },
    {
      name: "Cardano",
      symbol: "ADA",
      logo: "https://cryptologos.cc/logos/cardano-ada-logo.png",
    },
    {
      name: "Dogecoin",
      symbol: "DOGE",
      logo: "https://cryptologos.cc/logos/dogecoin-doge-logo.png",
    },
    {
      name: "Polygon",
      symbol: "MATIC",
      logo: "https://cryptologos.cc/logos/polygon-matic-logo.png",
    },
    {
      name: "Litecoin",
      symbol: "LTC",
      logo: "https://cryptologos.cc/logos/litecoin-ltc-logo.png",
    },
    {
      name: "Chainlink",
      symbol: "LINK",
      logo: "https://cryptologos.cc/logos/chainlink-link-logo.png",
    },
  ]

  return (
    <footer className="bg-gradient-to-br from-gray-900 via-slate-900 to-black dark:from-black dark:via-gray-950 dark:to-slate-950 text-white relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(34,197,94,0.1),rgba(0,0,0,0))]" />
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-[20%] left-[10%] w-64 h-64 rounded-full bg-gradient-to-r from-green-500/5 to-emerald-500/5 blur-3xl" />
        <div className="absolute bottom-[20%] right-[10%] w-80 h-80 rounded-full bg-gradient-to-r from-blue-500/5 to-cyan-500/5 blur-3xl" />
      </div>

      <div className="relative z-10">
        {/* Newsletter Section */}
        <div className="border-b border-white/10">
          <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16 py-16 md:py-20">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h3 className="text-3xl md:text-4xl font-bold mb-4">Stay Updated</h3>
                <p className="text-lg text-white/70 leading-relaxed">
                  Get the latest market insights, trading tips, and exclusive offers delivered to your inbox.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4">
                <Input
                  type="email"
                  placeholder="Enter your email address"
                  className="flex-1 rounded-xl border-white/20 bg-white/10 backdrop-blur-sm text-white placeholder:text-white/50 focus:border-green-400 focus:ring-green-400"
                />
                <Button className="rounded-xl bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 px-8 py-3 hover:scale-105 transition-all duration-300">
                  Subscribe
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Footer Content */}
        <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16 py-16 md:py-20">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-12">
            {/* Company Info */}
            <div className="lg:col-span-2">
              <div className="flex items-center gap-3 mb-6">
                <div className="w-12 h-12 rounded-2xl bg-gradient-to-r from-green-500 to-emerald-500 flex items-center justify-center shadow-lg">
                  <TrendingUp className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold">Apex Capital</h3>
                  <p className="text-sm text-white/60">Prop Trading Firm</p>
                </div>
              </div>
              <p className="text-white/70 mb-8 leading-relaxed">
                Apex Capital is a regulated proprietary trading firm providing institutional-grade funding solutions to professional traders worldwide. We combine cutting-edge technology with transparent evaluation processes to help skilled traders access capital and build sustainable trading careers.
              </p>

              {/* Contact Info */}
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-lg bg-white/10 flex items-center justify-center">
                    <Mail className="w-4 h-4 text-green-400" />
                  </div>
                  <span className="text-white/70"><EMAIL></span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-lg bg-white/10 flex items-center justify-center">
                    <Phone className="w-4 h-4 text-green-400" />
                  </div>
                  <span className="text-white/70">+****************</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-lg bg-white/10 flex items-center justify-center">
                    <MapPin className="w-4 h-4 text-green-400" />
                  </div>
                  <span className="text-white/70">New York, NY 10001</span>
                </div>
              </div>

              {/* Trust Indicators */}
              <div className="mt-8 grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-green-500/20 to-emerald-500/20 flex items-center justify-center mx-auto mb-2">
                    <Shield className="w-6 h-6 text-green-400" />
                  </div>
                  <div className="text-xs text-white/60">Regulated</div>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-blue-500/20 to-cyan-500/20 flex items-center justify-center mx-auto mb-2">
                    <Award className="w-6 h-6 text-blue-400" />
                  </div>
                  <div className="text-xs text-white/60">Award Winning</div>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-r from-purple-500/20 to-pink-500/20 flex items-center justify-center mx-auto mb-2">
                    <Clock className="w-6 h-6 text-purple-400" />
                  </div>
                  <div className="text-xs text-white/60">24/7 Support</div>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-lg font-bold mb-6">Quick Links</h4>
              <ul className="space-y-3">
                {quickLinks.map((link, index) => (
                  <li key={index}>
                    <Link
                      href={link.href}
                      className="text-white/70 hover:text-green-400 transition-colors duration-300 flex items-center gap-2 group"
                    >
                      <span className="group-hover:translate-x-1 transition-transform duration-300">{link.name}</span>
                      <ExternalLink className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Trading */}
            <div>
              <h4 className="text-lg font-bold mb-6">Trading</h4>
              <ul className="space-y-3">
                {tradingLinks.map((link, index) => (
                  <li key={index}>
                    <Link
                      href={link.href}
                      className="text-white/70 hover:text-green-400 transition-colors duration-300 flex items-center gap-2 group"
                    >
                      <span className="group-hover:translate-x-1 transition-transform duration-300">{link.name}</span>
                      <ExternalLink className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Support & Legal */}
            <div>
              <h4 className="text-lg font-bold mb-6">Support</h4>
              <ul className="space-y-3 mb-8">
                {supportLinks.slice(0, 4).map((link, index) => (
                  <li key={index}>
                    <Link
                      href={link.href}
                      className="text-white/70 hover:text-green-400 transition-colors duration-300 flex items-center gap-2 group"
                    >
                      <span className="group-hover:translate-x-1 transition-transform duration-300">{link.name}</span>
                      <ExternalLink className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </Link>
                  </li>
                ))}
              </ul>

              <h4 className="text-lg font-bold mb-6">Legal</h4>
              <ul className="space-y-3">
                {legalLinks.slice(0, 4).map((link, index) => (
                  <li key={index}>
                    <Link
                      href={link.href}
                      className="text-white/70 hover:text-green-400 transition-colors duration-300 flex items-center gap-2 group"
                    >
                      <span className="group-hover:translate-x-1 transition-transform duration-300">{link.name}</span>
                      <ExternalLink className="w-3 h-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Crypto Payment Section */}
        <div className="border-t border-white/10">
          <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16 py-12">
            <div className="text-center mb-8">
              <h4 className="text-xl font-bold text-white mb-4">We Accept 300+ Cryptocurrencies</h4>
              <p className="text-white/70">Trade and fund your account with your favorite digital currencies</p>
            </div>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {cryptoCurrencies.map((crypto, index) => (
                <div
                  key={index}
                  className="flex flex-col items-center gap-2 bg-white/10 hover:bg-white/20 rounded-xl p-4 transition-all duration-300 hover:scale-105 group"
                >
                  <div className="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center group-hover:bg-white/30 transition-colors duration-300">
                    <img
                      src={crypto.logo || "/placeholder.svg"}
                      alt={`${crypto.name} logo`}
                      className="w-6 h-6 object-contain"
                      crossOrigin="anonymous"
                    />
                  </div>
                  <div className="text-center">
                    <div className="text-sm font-bold text-white/90">{crypto.symbol}</div>
                    <div className="text-xs text-white/60">{crypto.name}</div>
                  </div>
                </div>
              ))}
            </div>
            <div className="text-center mt-8">
              <div className="inline-flex items-center gap-2 bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl px-6 py-3">
                <span className="text-lg font-bold text-green-400">+288 More Cryptocurrencies Supported</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-white/10">
          <div className="max-w-6xl mx-auto px-8 md:px-12 lg:px-16 py-8">
            <div className="flex flex-col md:flex-row items-center justify-between gap-6">
              {/* Copyright */}
              <div className="text-center md:text-left">
                <p className="text-white/60 text-sm">© {currentYear} Apex Capital. All rights reserved.</p>
                <p className="text-white/40 text-xs mt-1">
                  All trading is conducted in a simulated environment for educational purposes only.
                </p>
                <p className="text-white/40 text-xs mt-1">
                  Trading involves substantial risk and may not be suitable for all investors.
                </p>
              </div>

              {/* Social Links */}
              <div className="flex items-center gap-4">
                {socialLinks.map((social, index) => {
                  const IconComponent = social.icon
                  return (
                    <a
                      key={index}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-10 h-10 rounded-xl bg-white/10 hover:bg-gradient-to-r hover:from-green-500/20 hover:to-emerald-500/20 flex items-center justify-center transition-all duration-300 hover:scale-110 group"
                      aria-label={social.name}
                    >
                      <IconComponent className="w-5 h-5 text-white/70 group-hover:text-green-400 transition-colors duration-300" />
                    </a>
                  )
                })}
              </div>

              {/* Language Selector */}
              <div className="flex items-center gap-2">
                <Globe className="w-4 h-4 text-white/60" />
                <select className="bg-transparent text-white/70 text-sm border-none outline-none cursor-pointer hover:text-green-400 transition-colors duration-300">
                  <option value="en" className="bg-gray-900">
                    English
                  </option>
                  <option value="es" className="bg-gray-900">
                    Español
                  </option>
                  <option value="fr" className="bg-gray-900">
                    Français
                  </option>
                  <option value="de" className="bg-gray-900">
                    Deutsch
                  </option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
