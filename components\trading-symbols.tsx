"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { TrendingUp, TrendingDown, BarChart3, Activity, Globe, Zap } from "lucide-react"
import { useState, useEffect } from "react"

interface TradingSymbol {
  symbol: string
  name: string
  price: number
  change: number
  changePercent: number
  volume: string
  spread: number
  category: "major" | "minor" | "exotic" | "crypto" | "commodities" | "indices"
  flag1: string
  flag2?: string
}

export default function TradingSymbols() {
  const [selectedCategory, setSelectedCategory] = useState<string>("major")
  const [symbols, setSymbols] = useState<TradingSymbol[]>([])

  // Mock data - In real implementation, this would come from TradingView API
  const mockSymbols: TradingSymbol[] = [
    // Major Pairs
    {
      symbol: "EURUSD",
      name: "Euro / US Dollar",
      price: 1.0856,
      change: 0.0023,
      changePercent: 0.21,
      volume: "2.1B",
      spread: 0.8,
      category: "major",
      flag1: "🇪🇺",
      flag2: "🇺🇸",
    },
    {
      symbol: "GBPUSD",
      name: "British Pound / US Dollar",
      price: 1.2634,
      change: -0.0045,
      changePercent: -0.35,
      volume: "1.8B",
      spread: 1.2,
      category: "major",
      flag1: "🇬🇧",
      flag2: "🇺🇸",
    },
    {
      symbol: "USDJPY",
      name: "US Dollar / Japanese Yen",
      price: 149.85,
      change: 0.67,
      changePercent: 0.45,
      volume: "1.9B",
      spread: 0.9,
      category: "major",
      flag1: "🇺🇸",
      flag2: "🇯🇵",
    },
    {
      symbol: "USDCHF",
      name: "US Dollar / Swiss Franc",
      price: 0.8923,
      change: 0.0012,
      changePercent: 0.13,
      volume: "856M",
      spread: 1.1,
      category: "major",
      flag1: "🇺🇸",
      flag2: "🇨🇭",
    },
    {
      symbol: "AUDUSD",
      name: "Australian Dollar / US Dollar",
      price: 0.6587,
      change: -0.0034,
      changePercent: -0.51,
      volume: "743M",
      spread: 1.3,
      category: "major",
      flag1: "🇦🇺",
      flag2: "🇺🇸",
    },
    {
      symbol: "USDCAD",
      name: "US Dollar / Canadian Dollar",
      price: 1.3678,
      change: 0.0089,
      changePercent: 0.65,
      volume: "692M",
      spread: 1.4,
      category: "major",
      flag1: "🇺🇸",
      flag2: "🇨🇦",
    },
    {
      symbol: "NZDUSD",
      name: "New Zealand Dollar / US Dollar",
      price: 0.5934,
      change: -0.0021,
      changePercent: -0.35,
      volume: "421M",
      spread: 1.8,
      category: "major",
      flag1: "🇳🇿",
      flag2: "🇺🇸",
    },

    // Minor Pairs
    {
      symbol: "EURGBP",
      name: "Euro / British Pound",
      price: 0.8592,
      change: 0.0067,
      changePercent: 0.79,
      volume: "534M",
      spread: 1.6,
      category: "minor",
      flag1: "🇪🇺",
      flag2: "🇬🇧",
    },
    {
      symbol: "EURJPY",
      name: "Euro / Japanese Yen",
      price: 162.73,
      change: 0.89,
      changePercent: 0.55,
      volume: "487M",
      spread: 1.4,
      category: "minor",
      flag1: "🇪🇺",
      flag2: "🇯🇵",
    },
    {
      symbol: "GBPJPY",
      name: "British Pound / Japanese Yen",
      price: 189.34,
      change: -0.23,
      changePercent: -0.12,
      volume: "398M",
      spread: 2.1,
      category: "minor",
      flag1: "🇬🇧",
      flag2: "🇯🇵",
    },
    {
      symbol: "EURCHF",
      name: "Euro / Swiss Franc",
      price: 0.9687,
      change: 0.0034,
      changePercent: 0.35,
      volume: "312M",
      spread: 1.8,
      category: "minor",
      flag1: "🇪🇺",
      flag2: "🇨🇭",
    },
    {
      symbol: "GBPCHF",
      name: "British Pound / Swiss Franc",
      price: 1.1276,
      change: -0.0012,
      changePercent: -0.11,
      volume: "267M",
      spread: 2.3,
      category: "minor",
      flag1: "🇬🇧",
      flag2: "🇨🇭",
    },
    {
      symbol: "AUDCAD",
      name: "Australian Dollar / Canadian Dollar",
      price: 0.9012,
      change: -0.0067,
      changePercent: -0.74,
      volume: "189M",
      spread: 2.8,
      category: "minor",
      flag1: "🇦🇺",
      flag2: "🇨🇦",
    },

    // Exotic Pairs
    {
      symbol: "USDTRY",
      name: "US Dollar / Turkish Lira",
      price: 28.45,
      change: 0.34,
      changePercent: 1.21,
      volume: "145M",
      spread: 15.2,
      category: "exotic",
      flag1: "🇺🇸",
      flag2: "🇹🇷",
    },
    {
      symbol: "USDZAR",
      name: "US Dollar / South African Rand",
      price: 18.67,
      change: -0.23,
      changePercent: -1.22,
      volume: "98M",
      spread: 12.8,
      category: "exotic",
      flag1: "🇺🇸",
      flag2: "🇿🇦",
    },
    {
      symbol: "USDMXN",
      name: "US Dollar / Mexican Peso",
      price: 17.89,
      change: 0.12,
      changePercent: 0.67,
      volume: "87M",
      spread: 8.9,
      category: "exotic",
      flag1: "🇺🇸",
      flag2: "🇲🇽",
    },
    {
      symbol: "USDSEK",
      name: "US Dollar / Swedish Krona",
      price: 10.87,
      change: 0.08,
      changePercent: 0.74,
      volume: "76M",
      spread: 6.7,
      category: "exotic",
      flag1: "🇺🇸",
      flag2: "🇸🇪",
    },

    // Crypto
    {
      symbol: "BTCUSD",
      name: "Bitcoin / US Dollar",
      price: 43567.89,
      change: 1234.56,
      changePercent: 2.92,
      volume: "2.8B",
      spread: 25.0,
      category: "crypto",
      flag1: "₿",
    },
    {
      symbol: "ETHUSD",
      name: "Ethereum / US Dollar",
      price: 2687.45,
      change: -89.23,
      changePercent: -3.21,
      volume: "1.9B",
      spread: 8.5,
      category: "crypto",
      flag1: "Ξ",
    },
    {
      symbol: "ADAUSD",
      name: "Cardano / US Dollar",
      price: 0.4567,
      change: 0.0234,
      changePercent: 5.41,
      volume: "456M",
      spread: 0.002,
      category: "crypto",
      flag1: "₳",
    },

    // Commodities
    {
      symbol: "XAUUSD",
      name: "Gold / US Dollar",
      price: 2034.67,
      change: 12.45,
      changePercent: 0.62,
      volume: "1.2B",
      spread: 0.35,
      category: "commodities",
      flag1: "🥇",
    },
    {
      symbol: "XAGUSD",
      name: "Silver / US Dollar",
      price: 24.89,
      change: -0.34,
      changePercent: -1.35,
      volume: "234M",
      spread: 0.03,
      category: "commodities",
      flag1: "🥈",
    },
    {
      symbol: "USOIL",
      name: "Crude Oil WTI",
      price: 78.45,
      change: 2.34,
      changePercent: 3.08,
      volume: "567M",
      spread: 0.05,
      category: "commodities",
      flag1: "🛢️",
    },

    // Indices
    {
      symbol: "SPX500",
      name: "S&P 500",
      price: 4567.89,
      change: 23.45,
      changePercent: 0.52,
      volume: "3.4B",
      spread: 0.4,
      category: "indices",
      flag1: "🇺🇸",
    },
    {
      symbol: "NAS100",
      name: "NASDAQ 100",
      price: 15678.34,
      change: -89.23,
      changePercent: -0.57,
      volume: "2.1B",
      spread: 1.2,
      category: "indices",
      flag1: "🇺🇸",
    },
    {
      symbol: "GER40",
      name: "DAX 40",
      price: 16234.56,
      change: 156.78,
      changePercent: 0.98,
      volume: "1.8B",
      spread: 2.1,
      category: "indices",
      flag1: "🇩🇪",
    },
  ]

  const categories = [
    { id: "major", name: "Major Pairs", icon: Globe, description: "Most traded currency pairs" },
    { id: "minor", name: "Minor Pairs", icon: BarChart3, description: "Cross-currency pairs" },
    { id: "exotic", name: "Exotic Pairs", icon: Activity, description: "Emerging market currencies" },
    { id: "crypto", name: "Crypto", icon: Zap, description: "Digital currencies" },
    { id: "commodities", name: "Commodities", icon: TrendingUp, description: "Gold, Oil, Silver" },
    { id: "indices", name: "Indices", icon: BarChart3, description: "Stock market indices" },
  ]

  useEffect(() => {
    // Filter symbols based on selected category
    const filtered = mockSymbols.filter((symbol) => symbol.category === selectedCategory)
    setSymbols(filtered)
  }, [selectedCategory])

  const formatPrice = (price: number, symbol: string) => {
    if (symbol.includes("JPY")) {
      return price.toFixed(2)
    } else if (symbol.includes("BTC")) {
      return price.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })
    } else if (symbol.includes("ETH")) {
      return price.toFixed(2)
    } else if (symbol.includes("XAU") || symbol.includes("SPX") || symbol.includes("NAS") || symbol.includes("GER")) {
      return price.toFixed(2)
    }
    return price.toFixed(4)
  }

  const formatChange = (change: number, symbol: string) => {
    if (symbol.includes("JPY")) {
      return change > 0 ? `+${change.toFixed(2)}` : change.toFixed(2)
    } else if (symbol.includes("BTC") || symbol.includes("ETH")) {
      return change > 0 ? `+${change.toFixed(2)}` : change.toFixed(2)
    } else if (symbol.includes("XAU") || symbol.includes("SPX") || symbol.includes("NAS") || symbol.includes("GER")) {
      return change > 0 ? `+${change.toFixed(2)}` : change.toFixed(2)
    }
    return change > 0 ? `+${change.toFixed(4)}` : change.toFixed(4)
  }

  return (
    <section className="py-24 md:py-32 relative" aria-labelledby="symbols-heading">
      <div className="max-w-7xl mx-auto px-8 md:px-12 lg:px-16">
        {/* Section Header */}
        <div className="mb-20 md:mb-28 max-w-4xl mx-auto text-center">
          <Badge
            variant="outline"
            className="mb-8 md:mb-12 text-sm font-light border-gray-300 dark:border-white/20 text-gray-600 dark:text-white/80 px-4 py-2 items-center"
          >
            <BarChart3 className="w-4 h-4 mr-2" />
            Live Market Data
          </Badge>
          <h2 id="symbols-heading" className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 md:mb-12 leading-tight">
            Trade{" "}
            <span className="bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-400 dark:to-emerald-400 bg-clip-text text-transparent">
              40+ instruments
            </span>
          </h2>
          <p className="text-lg md:text-xl lg:text-2xl text-gray-700 dark:text-white/70 leading-relaxed">
            Access real-time prices for forex pairs, cryptocurrencies, commodities, and indices. Trade with tight
            spreads and fast execution.
          </p>
        </div>

        {/* Category Tabs */}
        <div className="mb-12 md:mb-16">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => {
              const IconComponent = category.icon
              const isActive = selectedCategory === category.id

              return (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center gap-3 px-6 py-4 rounded-2xl transition-all duration-300 hover:scale-105 ${
                    isActive
                      ? "bg-gradient-to-r from-green-500/20 to-emerald-500/20 dark:from-green-500/30 dark:to-emerald-500/30 border-2 border-green-300 dark:border-green-400/40 text-green-700 dark:text-green-300"
                      : "bg-white/80 dark:bg-white/5 border border-gray-200 dark:border-white/10 text-gray-600 dark:text-white/70 hover:bg-white/90 dark:hover:bg-white/10"
                  }`}
                >
                  <IconComponent className="w-5 h-5" />
                  <div className="text-left">
                    <div className="text-sm font-semibold">{category.name}</div>
                    <div className="text-xs opacity-70">{category.description}</div>
                  </div>
                </button>
              )
            })}
          </div>
        </div>

        {/* Symbols Table */}
        <div className="bg-white/80 dark:bg-white/5 backdrop-blur-sm border border-gray-200 dark:border-white/10 rounded-3xl overflow-hidden shadow-xl">
          {/* Table Header */}
          <div className="bg-gradient-to-r from-gray-50/80 to-gray-100/80 dark:from-white/5 dark:to-white/10 px-6 py-4 border-b border-gray-200 dark:border-white/10">
            <div className="grid grid-cols-12 gap-4 items-center text-sm font-semibold text-gray-600 dark:text-white/70">
              <div className="col-span-4 md:col-span-3">Symbol</div>
              <div className="col-span-2 text-right">Price</div>
              <div className="col-span-2 text-right">Change</div>
              <div className="col-span-2 text-right hidden md:block">Volume</div>
              <div className="col-span-2 md:col-span-1 text-right hidden md:block">Spread</div>
              <div className="col-span-2 md:col-span-1 text-center">Action</div>
            </div>
          </div>

          {/* Table Body */}
          <div className="divide-y divide-gray-200 dark:divide-white/10">
            {symbols.map((symbol, index) => (
              <div
                key={symbol.symbol}
                className="px-6 py-4 hover:bg-gray-50/50 dark:hover:bg-white/5 transition-colors duration-200 group"
              >
                <div className="grid grid-cols-12 gap-4 items-center">
                  {/* Symbol */}
                  <div className="col-span-4 md:col-span-3">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-1">
                        <span className="text-lg">{symbol.flag1}</span>
                        {symbol.flag2 && <span className="text-lg">{symbol.flag2}</span>}
                      </div>
                      <div>
                        <div className="text-sm font-bold text-gray-900 dark:text-white group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-200">
                          {symbol.symbol}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-white/50 truncate">{symbol.name}</div>
                      </div>
                    </div>
                  </div>

                  {/* Price */}
                  <div className="col-span-2 text-right">
                    <div className="text-sm font-bold text-gray-900 dark:text-white">
                      {formatPrice(symbol.price, symbol.symbol)}
                    </div>
                  </div>

                  {/* Change */}
                  <div className="col-span-2 text-right">
                    <div
                      className={`text-sm font-bold flex items-center justify-end gap-1 ${
                        symbol.change > 0
                          ? "text-green-600 dark:text-green-400"
                          : symbol.change < 0
                            ? "text-red-600 dark:text-red-400"
                            : "text-gray-600 dark:text-gray-400"
                      }`}
                    >
                      {symbol.change > 0 ? (
                        <TrendingUp className="w-3 h-3" />
                      ) : symbol.change < 0 ? (
                        <TrendingDown className="w-3 h-3" />
                      ) : null}
                      <div>
                        <div>{formatChange(symbol.change, symbol.symbol)}</div>
                        <div className="text-xs">
                          ({symbol.changePercent > 0 ? "+" : ""}
                          {symbol.changePercent.toFixed(2)}%)
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Volume */}
                  <div className="col-span-2 text-right hidden md:block">
                    <div className="text-sm text-gray-600 dark:text-white/70">{symbol.volume}</div>
                  </div>

                  {/* Spread */}
                  <div className="col-span-1 text-right hidden md:block">
                    <div className="text-sm text-gray-600 dark:text-white/70">{symbol.spread}</div>
                  </div>

                  {/* Action */}
                  <div className="col-span-2 md:col-span-1 text-center">
                    <Button
                      size="sm"
                      className="bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-500 dark:to-emerald-500 text-white hover:from-green-700 hover:to-emerald-700 dark:hover:from-green-600 dark:hover:to-emerald-600 px-3 py-1 text-xs rounded-lg hover:scale-105 transition-all duration-300"
                    >
                      Trade
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Market Stats */}
        <div className="mt-16 md:mt-20">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20 backdrop-blur-sm border border-green-200 dark:border-green-400/20 rounded-2xl p-6 text-center">
              <div className="text-2xl md:text-3xl font-bold text-green-600 dark:text-green-400 mb-2">40+</div>
              <div className="text-sm text-gray-600 dark:text-white/60">Trading Instruments</div>
            </div>
            <div className="bg-gradient-to-r from-blue-500/10 to-cyan-500/10 dark:from-blue-500/20 dark:to-cyan-500/20 backdrop-blur-sm border border-blue-200 dark:border-blue-400/20 rounded-2xl p-6 text-center">
              <div className="text-2xl md:text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">0.8</div>
              <div className="text-sm text-gray-600 dark:text-white/60">Avg Spread (pips)</div>
            </div>
            <div className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 dark:from-purple-500/20 dark:to-pink-500/20 backdrop-blur-sm border border-purple-200 dark:border-purple-400/20 rounded-2xl p-6 text-center">
              <div className="text-2xl md:text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">{"<1ms"}</div>
              <div className="text-sm text-gray-600 dark:text-white/60">Execution Speed</div>
            </div>
            <div className="bg-gradient-to-r from-yellow-500/10 to-orange-500/10 dark:from-yellow-500/20 dark:to-orange-500/20 backdrop-blur-sm border border-yellow-200 dark:border-yellow-400/20 rounded-2xl p-6 text-center">
              <div className="text-2xl md:text-3xl font-bold text-yellow-600 dark:text-yellow-400 mb-2">24/7</div>
              <div className="text-sm text-gray-600 dark:text-white/60">Market Access</div>
            </div>
          </div>
        </div>

        {/* CTA */}
        <div className="mt-16 md:mt-20 text-center">
          <div className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 dark:from-green-500/20 dark:to-emerald-500/20 backdrop-blur-sm border border-green-200 dark:border-green-400/20 rounded-3xl p-8 md:p-12 shadow-xl relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 dark:from-green-500/10 dark:to-emerald-500/10" />
            <div className="relative z-10">
              <h3 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">
                Ready to Start Trading?
              </h3>
              <p className="text-lg text-gray-700 dark:text-white/70 mb-8 max-w-2xl mx-auto">
                Get funded up to $200K and trade these instruments with our capital. Pass our evaluation and start
                earning up to 90% of your profits.
              </p>
              <Button className="rounded-full bg-gradient-to-r from-green-600 to-emerald-600 dark:from-green-500 dark:to-emerald-500 text-white hover:from-green-700 hover:to-emerald-700 dark:hover:from-green-600 dark:hover:to-emerald-600 px-8 py-4 text-lg hover:scale-105 transition-all duration-300 hover:shadow-lg">
                Start Trading Challenge
                <BarChart3 className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
